#include "modelinference.h"
#include <QCoreApplication>
#include <QStandardPaths>
#include <QJsonParseError>

const int ModelInference::DEFAULT_TIMEOUT_SECONDS = 30;
const QString ModelInference::INFERENCE_EXECUTABLE = "rknn_inference_demo";

// InferenceWorker 实现
InferenceWorker::InferenceWorker(QObject *parent)
    : QObject(parent)
    , m_process(nullptr)
{
    setupTempDirectory();
}

InferenceWorker::~InferenceWorker()
{
    if (m_process) {
        m_process->kill();
        m_process->waitForFinished(3000);
        delete m_process;
    }
}

void InferenceWorker::setupTempDirectory()
{
    m_tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/diagnosis_inference";
    QDir dir;
    if (!dir.exists(m_tempDir)) {
        dir.mkpath(m_tempDir);
    }
}

void InferenceWorker::runInference(const QString& imagePath, const QString& symptomText, 
                                  const QString& modelPath, const QString& configPath)
{
    Q_UNUSED(configPath)
    
    qDebug() << "Starting inference with image:" << imagePath << "model:" << modelPath;
    
    // 检查输入文件
    if (!QFile::exists(imagePath)) {
        emit inferenceError("图片文件不存在: " + imagePath);
        return;
    }
    
    if (!QFile::exists(modelPath)) {
        emit inferenceError("模型文件不存在: " + modelPath);
        return;
    }
    
    // 创建输入JSON文件
    QString inputJsonPath = createInputJson(imagePath, symptomText);
    if (inputJsonPath.isEmpty()) {
        emit inferenceError("创建输入配置文件失败");
        return;
    }
    
    // 输出JSON文件路径
    QString outputJsonPath = m_tempDir + "/output_" + 
                            QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".json";
    
    // 检查推理程序是否存在
    QString executablePath = QCoreApplication::applicationDirPath() + "/" + INFERENCE_EXECUTABLE;
    if (!QFile::exists(executablePath)) {
        // 尝试在系统路径中查找
        executablePath = INFERENCE_EXECUTABLE;
    }
    
    // 构建推理命令
    QStringList arguments;
    arguments << modelPath << inputJsonPath << outputJsonPath;
    
    // 创建进程
    m_process = new QProcess(this);
    
    // 连接信号
    connect(m_process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, outputJsonPath](int exitCode, QProcess::ExitStatus exitStatus) {
                Q_UNUSED(exitStatus)
                
                if (exitCode == 0) {
                    // 解析输出结果
                    InferenceResult result = parseOutputJson(outputJsonPath);
                    if (result.success) {
                        emit inferenceCompleted(result);
                    } else {
                        emit inferenceError("推理结果解析失败: " + result.errorMessage);
                    }
                } else {
                    QString errorOutput = m_process->readAllStandardError();
                    emit inferenceError("推理程序执行失败: " + errorOutput);
                }
                
                // 清理进程
                m_process->deleteLater();
                m_process = nullptr;
            });
    
    connect(m_process, &QProcess::errorOccurred,
            [this](QProcess::ProcessError error) {
                QString errorMsg;
                switch (error) {
                case QProcess::FailedToStart:
                    errorMsg = "推理程序启动失败，请检查程序是否存在";
                    break;
                case QProcess::Crashed:
                    errorMsg = "推理程序崩溃";
                    break;
                case QProcess::Timedout:
                    errorMsg = "推理程序超时";
                    break;
                default:
                    errorMsg = "推理程序未知错误";
                    break;
                }
                emit inferenceError(errorMsg);
                
                if (m_process) {
                    m_process->deleteLater();
                    m_process = nullptr;
                }
            });
    
    // 启动推理进程
    emit progressUpdated(10);
    
    // 如果推理程序不存在，使用模拟结果
    if (!QFile::exists(executablePath)) {
        qWarning() << "Inference executable not found, using mock result";
        QTimer::singleShot(2000, [this, imagePath, symptomText]() {
            InferenceResult mockResult = createMockResult(imagePath, symptomText);
            emit inferenceCompleted(mockResult);
        });
        return;
    }
    
    qDebug() << "Starting inference process:" << executablePath << arguments;
    m_process->start(executablePath, arguments);
    
    if (!m_process->waitForStarted(5000)) {
        emit inferenceError("推理程序启动超时");
        return;
    }
    
    emit progressUpdated(30);
}

QString InferenceWorker::createInputJson(const QString& imagePath, const QString& symptomText) const
{
    QString inputJsonPath = m_tempDir + "/input_" + 
                           QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".json";
    
    QJsonObject inputObj;
    inputObj["image_path"] = imagePath;
    inputObj["symptom_text"] = symptomText;
    inputObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(inputObj);
    
    QFile file(inputJsonPath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot create input JSON file:" << inputJsonPath;
        return QString();
    }
    
    file.write(doc.toJson());
    file.close();
    
    qDebug() << "Created input JSON:" << inputJsonPath;
    return inputJsonPath;
}

InferenceResult InferenceWorker::parseOutputJson(const QString& outputPath) const
{
    InferenceResult result;
    
    if (!QFile::exists(outputPath)) {
        result.success = false;
        result.errorMessage = "输出文件不存在";
        return result;
    }
    
    QFile file(outputPath);
    if (!file.open(QIODevice::ReadOnly)) {
        result.success = false;
        result.errorMessage = "无法读取输出文件";
        return result;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        result.success = false;
        result.errorMessage = "输出JSON格式错误: " + parseError.errorString();
        return result;
    }
    
    if (!doc.isObject()) {
        result.success = false;
        result.errorMessage = "输出JSON不是有效对象";
        return result;
    }
    
    result = InferenceResult::fromJson(doc.object());
    return result;
}

InferenceResult InferenceWorker::createMockResult(const QString& imagePath, const QString& symptomText) const
{
    InferenceResult result;
    result.success = true;
    result.confidence = 0.85f;
    
    // 根据症状生成模拟结果
    if (symptomText.contains("发热") || symptomText.contains("头痛") || symptomText.contains("咳嗽")) {
        result.detectedMedicine = "999感冒灵颗粒";
        result.dosageInstructions = "口服，成人一次1袋，一日3次";
        result.frequency = "一日三次";
        result.timing = "饭后服用";
        result.contraindications = "孕妇、哺乳期妇女禁用";
        result.sideEffects = "偶见恶心、呕吐、皮疹";
        result.warnings = {"服药期间不得饮酒", "不宜与其他解热镇痛药同用"};
    } else if (symptomText.contains("胃痛") || symptomText.contains("胃胀")) {
        result.detectedMedicine = "奥美拉唑肠溶胶囊";
        result.dosageInstructions = "口服，成人一次20mg，一日1-2次";
        result.frequency = "一日1-2次";
        result.timing = "餐前服用";
        result.contraindications = "对本品过敏者禁用";
        result.sideEffects = "偶见头痛、腹泻、皮疹";
        result.warnings = {"长期服用需定期检查", "与其他药物间隔2小时服用"};
    } else {
        result.detectedMedicine = "维生素C片";
        result.dosageInstructions = "口服，成人一次100mg，一日3次";
        result.frequency = "一日三次";
        result.timing = "餐后服用";
        result.contraindications = "无明显禁忌";
        result.sideEffects = "大剂量服用可能引起腹泻";
        result.warnings = {"不宜与碱性药物同服"};
    }
    
    // 设置图片路径（使用原图片）
    result.medicineImage = imagePath;
    
    qDebug() << "Created mock result for:" << result.detectedMedicine;
    return result;
}

// ModelInference 实现
ModelInference::ModelInference(QObject *parent)
    : QObject(parent)
    , m_workerThread(nullptr)
    , m_worker(nullptr)
    , m_isInferencing(false)
    , m_progress(0)
    , m_timeoutSeconds(DEFAULT_TIMEOUT_SECONDS)
    , m_timeoutTimer(nullptr)
{
    setupWorkerThread();
    
    // 创建超时定时器
    m_timeoutTimer = new QTimer(this);
    m_timeoutTimer->setSingleShot(true);
    connect(m_timeoutTimer, &QTimer::timeout, this, &ModelInference::onTimeout);
    
    qDebug() << "ModelInference initialized";
}

ModelInference::~ModelInference()
{
    cleanupWorkerThread();
    qDebug() << "ModelInference destroyed";
}

void ModelInference::setupWorkerThread()
{
    m_workerThread = new QThread(this);
    m_worker = new InferenceWorker();
    m_worker->moveToThread(m_workerThread);
    
    // 连接信号
    connect(this, &ModelInference::destroyed, m_worker, &InferenceWorker::deleteLater);
    connect(m_worker, &InferenceWorker::inferenceCompleted, this, &ModelInference::onInferenceCompleted);
    connect(m_worker, &InferenceWorker::inferenceError, this, &ModelInference::onInferenceError);
    connect(m_worker, &InferenceWorker::progressUpdated, this, &ModelInference::onProgressUpdated);
    
    m_workerThread->start();
    qDebug() << "Worker thread started";
}

void ModelInference::cleanupWorkerThread()
{
    if (m_workerThread) {
        m_workerThread->quit();
        if (!m_workerThread->wait(3000)) {
            m_workerThread->terminate();
            m_workerThread->wait(1000);
        }
        m_workerThread = nullptr;
    }
    m_worker = nullptr;
}

void ModelInference::startInference(const QString& imagePath, const QString& symptomText, 
                                   const TaskData& taskData)
{
    if (m_isInferencing) {
        qWarning() << "Inference already in progress";
        return;
    }
    
    if (!validateInputs(imagePath, symptomText)) {
        emit inferenceError("输入参数验证失败");
        return;
    }
    
    m_isInferencing = true;
    m_progress = 0;
    
    // 启动超时定时器
    m_timeoutTimer->start(m_timeoutSeconds * 1000);
    
    emit inferenceStarted();
    emit progressUpdated(0);
    
    // 在工作线程中执行推理
    QMetaObject::invokeMethod(m_worker, "runInference", Qt::QueuedConnection,
                             Q_ARG(QString, imagePath),
                             Q_ARG(QString, symptomText),
                             Q_ARG(QString, taskData.modelPath),
                             Q_ARG(QString, taskData.configPath));
    
    qDebug() << "Inference started for task:" << taskData.taskName;
}

void ModelInference::stopInference()
{
    if (!m_isInferencing) {
        return;
    }
    
    m_isInferencing = false;
    m_timeoutTimer->stop();
    
    // 这里可以添加停止推理的逻辑
    qDebug() << "Inference stopped";
}

bool ModelInference::validateInputs(const QString& imagePath, const QString& symptomText) const
{
    if (imagePath.isEmpty() || !QFile::exists(imagePath)) {
        qWarning() << "Invalid image path:" << imagePath;
        return false;
    }
    
    if (symptomText.trimmed().isEmpty()) {
        qWarning() << "Empty symptom text";
        return false;
    }
    
    return true;
}

QString ModelInference::getModelExecutablePath(const QString& modelPath) const
{
    Q_UNUSED(modelPath)
    
    // 首先尝试应用程序目录
    QString execPath = QCoreApplication::applicationDirPath() + "/" + INFERENCE_EXECUTABLE;
    if (QFile::exists(execPath)) {
        return execPath;
    }
    
    // 尝试系统路径
    return INFERENCE_EXECUTABLE;
}

void ModelInference::onInferenceCompleted(const InferenceResult& result)
{
    m_isInferencing = false;
    m_timeoutTimer->stop();
    m_progress = 100;
    
    emit progressUpdated(100);
    emit inferenceCompleted(result);
    
    qDebug() << "Inference completed successfully";
}

void ModelInference::onInferenceError(const QString& errorMessage)
{
    m_isInferencing = false;
    m_timeoutTimer->stop();
    m_progress = 0;
    
    emit inferenceError(errorMessage);
    
    qWarning() << "Inference error:" << errorMessage;
}

void ModelInference::onProgressUpdated(int percentage)
{
    m_progress = percentage;
    emit progressUpdated(percentage);
}

void ModelInference::onTimeout()
{
    if (m_isInferencing) {
        m_isInferencing = false;
        emit timeoutOccurred();
        emit inferenceError(QString("推理超时（%1秒）").arg(m_timeoutSeconds));
        qWarning() << "Inference timeout after" << m_timeoutSeconds << "seconds";
    }
}
