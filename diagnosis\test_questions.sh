#!/bin/bash

echo "=== 测试题目文件加载 ==="

# 检查题目文件是否存在
echo "1. 检查题目文件..."
if [ -f "config/questions.json" ]; then
    echo "✓ 找到题目文件: config/questions.json"
    echo "文件大小: $(wc -c < config/questions.json) 字节"
else
    echo "✗ 未找到题目文件: config/questions.json"
    exit 1
fi

# 检查JSON格式
echo ""
echo "2. 检查JSON格式..."
if python3 -m json.tool config/questions.json > /dev/null 2>&1; then
    echo "✓ JSON格式正确"
else
    echo "✗ JSON格式错误"
    echo "尝试显示文件内容:"
    cat config/questions.json
    exit 1
fi

# 显示题目内容
echo ""
echo "3. 题目内容预览..."
python3 -c "
import json
with open('config/questions.json', 'r', encoding='utf-8') as f:
    questions = json.load(f)
    print(f'总共 {len(questions)} 道题目:')
    for i, q in enumerate(questions, 1):
        print(f'题目 {i}:')
        print(f'  Q: {q.get(\"Q\", \"N/A\")[:50]}...')
        print(f'  A: {q.get(\"A\", \"N/A\")}')
        print(f'  图片: {len(q.get(\"image\", []))} 张')
        print()
"

# 检查build目录中的题目文件
echo ""
echo "4. 检查build目录..."
if [ -f "build/config/questions.json" ]; then
    echo "✓ build目录中存在题目文件"
    if cmp -s "config/questions.json" "build/config/questions.json"; then
        echo "✓ 文件内容一致"
    else
        echo "✗ 文件内容不一致"
        echo "复制最新文件到build目录..."
        cp config/questions.json build/config/questions.json
        echo "✓ 文件已更新"
    fi
else
    echo "✗ build目录中缺少题目文件"
    echo "复制文件到build目录..."
    mkdir -p build/config
    cp config/questions.json build/config/questions.json
    echo "✓ 文件已复制"
fi

echo ""
echo "=== 测试完成 ==="
