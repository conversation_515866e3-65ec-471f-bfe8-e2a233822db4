﻿#include "stdafx.h"
#include "yxPermission.h"
#include "YXDishCore.h"
#include <QCryptographicHash>
#include <QtCore/QtCore>
#include "SFCoreIntf.h"
#pragma comment(lib, "sf-core-ls.lib")

const char* g_salt = "ABCDEF!@#$%^";

//#include <Shlobj.h>
#include <stdio.h>
#include <locale.h>
#include <fstream>
#include <iostream>
#include <time.h>
#include <io.h>
#include <direct.h>
#include <windows.h>

//////////////////////////////////////////////////////////////////////////
std::wstring StringToWString(const std::string& str)
{
    int num = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, NULL, 0);
    wchar_t *wide = new wchar_t[num];
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, wide, num);
    std::wstring w_str(wide);
    delete[] wide;
    return w_str;
}

std::string WStringToString(const std::wstring &wstr)
{
    std::string str;
    int nLen = (int)wstr.length();
    str.resize(nLen, ' ');
    int nResult = WideCharToMultiByte(CP_ACP, 0, (LPCWSTR)wstr.c_str(), nLen, (LPSTR)str.c_str(), nLen, NULL, NULL);
    if (nResult == 0)
    {
        return "";
    }
    return str;
}

std::string GetMachineGUID()
{
    std::string str = "";
    HKEY hKey;
    LPCWSTR regUrl = L"SOFTWARE\\Microsoft\\Cryptography";
    LPCWSTR regKey = L"MachineGuid";
    if (ERROR_SUCCESS == ::RegOpenKeyEx(HKEY_LOCAL_MACHINE, regUrl, 0, KEY_READ | KEY_WOW64_64KEY, &hKey))
    {
        DWORD dwSize = 0;
        DWORD dwDataType = 0;
        if (ERROR_SUCCESS == ::RegQueryValueEx(hKey, regKey, 0, &dwDataType, NULL, &dwSize))
        {
            wchar_t * buf = new wchar_t[dwSize];
            memset(buf, 0, dwSize * sizeof(wchar_t));
            ::RegQueryValueEx(hKey, regKey, 0, &dwDataType, (LPBYTE)buf, &dwSize);

            str = WStringToString(buf);
            delete[] buf;
        }

        ::RegCloseKey(hKey);
    }

    return str;
}

BOOL GetDeviceInfoByCmd(char *&lpszResult, const LPWSTR cmdLine, const string& key)
{
    const long COMMAND_SIZE = 1020; // Command line output buffer
    const DWORD WAIT_TIME = 500; // INFINITE
    const std::string strEnSearch = key;

    BOOL fReturnCode = FALSE;
    HANDLE hReadPipe = NULL;  // Pipeline for READ
    HANDLE hWritePipe = NULL; // Pipeline for WRITE
    PROCESS_INFORMATION pi;   // Process information
    STARTUPINFO			si;   // Control-command window info
    SECURITY_ATTRIBUTES sa;   // Security attributes

    char szBuffer[COMMAND_SIZE + 1] = { 0 }; // Command line output buffer
    std::string strBuffer;
    DWORD count = 0;
    size_t pos = 0;
    size_t i = 0;
    size_t j = 0;

    lpszResult = (char*)malloc((COMMAND_SIZE + 1) * sizeof(char));
    memset(lpszResult, 0x00, (COMMAND_SIZE + 1) * sizeof(char));

    memset(&pi, 0, sizeof(pi));
    memset(&si, 0, sizeof(si));
    memset(&sa, 0, sizeof(sa));

    pi.hProcess = NULL;
    pi.hThread = NULL;
    si.cb = sizeof(STARTUPINFO);
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.lpSecurityDescriptor = NULL;
    sa.bInheritHandle = TRUE;

    // Step 1: Create pipeline
    fReturnCode = CreatePipe(&hReadPipe, &hWritePipe, &sa, 0);
    if (!fReturnCode)
    {
        goto EXIT;
    }

    // Step 2: Set command line window to be specific READ / WRITE pipeline
    GetStartupInfo(&si);
    si.hStdError = hWritePipe;
    si.hStdOutput = hWritePipe;
    si.wShowWindow = SW_HIDE; // hide command line window
    si.dwFlags = STARTF_USESHOWWINDOW | STARTF_USESTDHANDLES;

    // Step 3: Create process to get command line handle
    fReturnCode = CreateProcess(NULL, cmdLine, NULL, NULL, TRUE, 0, NULL, NULL, &si, &pi);
    if (!fReturnCode)
    {
        goto EXIT;
    }

    // Step 4: Get return back data
    WaitForSingleObject(pi.hProcess, WAIT_TIME);
    fReturnCode = ReadFile(hReadPipe, szBuffer, COMMAND_SIZE, &count, 0);
    if (!fReturnCode)
    {
        goto EXIT;
    }

    // Step 5: Search for mainboard serial number
    fReturnCode = FALSE;
    strBuffer = szBuffer;
    pos = strBuffer.find(strEnSearch);

    if (pos < 0) // NOT FOUND
    {
        goto EXIT;
    }
    else
    {
        strBuffer = strBuffer.substr(pos + strEnSearch.length());
    }

    memset(szBuffer, 0x00, sizeof(szBuffer));
    strcpy_s(szBuffer, strBuffer.c_str());

    // Get ride of <space>, \r, \n
    j = 0;
    for (i = 0; i < strlen(szBuffer); i++)
    {
        if (szBuffer[i] != ' ' && szBuffer[i] != '\n' && szBuffer[i] != '\r')
        {
            lpszResult[j] = szBuffer[i];
            j++;
        }
    }

    fReturnCode = TRUE;

EXIT:
    CloseHandle(hWritePipe);
    CloseHandle(hReadPipe);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    return(fReturnCode);
}

BOOL GetBaseBoardUuidByCmd(char *&lpszBaseBoard)
{
    WCHAR szFetCmd[] = L"wmic csproduct get uuid";
    string key = "UUID";

    return GetDeviceInfoByCmd(lpszBaseBoard, szFetCmd, key);
}

BOOL GetCpuNameByCmd(char *&lpszCpuName)
{
    WCHAR szFetCmd[] = L"wmic cpu get name";
    string key = "Name";

    return GetDeviceInfoByCmd(lpszCpuName, szFetCmd, key);
}
//////////////////////////////////////////////////////////////////////////

std::string yxPermission::m_key = "";

std::string yxPermission::getCpuName()
{
    char *cpuName = nullptr;
    if (GetCpuNameByCmd(cpuName))
    {
        return cpuName;
    }

    return "";
}

std::string yxPermission::getDeviceInfo()
{
    return getDeviceNo();

#ifdef MODE_EDGE_END_DOG
    if (m_key.empty())
    {
        QString uuid = QUuid::createUuid().toString();
        QString key = uuid.mid(1, uuid.length() - 2);
        m_key = CSTR(key);
    }
    return m_key;
#endif // MODE_EDGE_END_DOG



    //return "1836364c5bad4fd68ebe7474d177f6ce";
    //return "dfd8715de207338b06b19bed38ea987f";
#ifdef _DEBUG
    //return "9b177d6d-3d69-4c62-a03f-52f1bbe2e1a2";
    //return "dfd8715de207338b06b19bed38ea987f";
#endif
    char *lpszMainBoardUuid = NULL;
    int nTimes = 0;
    while (nTimes++ < 10)
    {
        if (!GetBaseBoardUuidByCmd(lpszMainBoardUuid)) {
            continue;
        }

        if (strlen(lpszMainBoardUuid) > 0)
        {
            break;
        }
    }
    QByteArray data;
    data.append(lpszMainBoardUuid);
    data.append(g_salt);

    QCryptographicHash alg(QCryptographicHash::Md5);
    alg.addData(data);
    QByteArray md5 = alg.result().toHex();
    QString s(md5);
    return s.toStdString();
}

std::string yxPermission::getDeviceNo()
{
    //return GetMachineGUID();
    return ::getDeviceNo();
}

std::string yxPermission::getProductNo()
{
#ifdef PLATE
    return "20002";
#else
    return "10002";
#endif // PLATE
}

std::string yxPermission::getDeviceLicense(const std::string &key)
{
    std::string p = key + "245ba9b4-a074-499c-974c-5377b15c699f";
    QByteArray data;
    data.append(QSTR(p));

    QCryptographicHash alg(QCryptographicHash::Sha256);
    alg.addData(data);
    QByteArray md5 = alg.result().toHex();
    QString s(md5);
    return s.toStdString();
}

void yxPermission::setAppAuth(const std::string &appAuth)
{
    ::setAppAuth(appAuth);
}

bool yxPermission::activate(const std::string &licenseNo, std::string &error)
{
    DeviceVM vm;
    vm.deviceNo = ::getDeviceNo();
    vm.productNo = getProductNo();
    vm.licenseNo = licenseNo;

    SF_LICENSE_STATE st = ::callServerToActivate(vm);
    error = ::getErrorMessage(st);
    qInfo() << QString("activate(%1): %2").arg(QString::fromStdString(licenseNo)).arg(st);
    return st == SF_OK;
}

bool yxPermission::deactive(std::string &error)
{
    SF_LICENSE_STATE st = ::deactivate(getProductNo());
    error = ::getErrorMessage(st);
    qInfo() << "deactive: " << st;
    return st == SF_OK;
}

bool yxPermission::checkLicense(bool bCheckCloud /*= false*/)
{
    SF_LICENSE_STATE st = ::checkLicense(getProductNo(), bCheckCloud);
    if (st != SF_OK)
    {
        qWarning() << "checkLicense error: " << QString::fromLocal8Bit(::getErrorMessage(st).c_str());
    }

    return SF_OK == st;
}

bool yxPermission::licenseInfo(QString &storeNo, QString &license)
{
    DeviceVM vm;
    if (SF_OK == ::getLicenseInfo(getProductNo(), vm))
    {
        storeNo = QString::fromStdString(vm.storeNo);
        license = QString::fromStdString(vm.licenseNo);
        return true;
    }

    return false;
}

