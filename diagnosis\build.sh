#!/bin/bash
# 药盒识别与用药建议应用编译脚本
# 基于autobit_back配置，适用于Linux主机环境
# 作者：AI助手
# 日期：$(date +%Y-%m-%d)

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="DiagnosisApp"
BUILD_DIR="${SCRIPT_DIR}/build"
QT_PATH="${QT_PATH:-/opt/Qt5.12.6/5.12.6/gcc_64}"

echo_info "=== 药盒识别与用药建议应用编译脚本 ==="
echo_info "项目目录: ${SCRIPT_DIR}"
echo_info "构建目录: ${BUILD_DIR}"

# 检查Qt环境
check_qt_environment() {
    echo_info "检查Qt环境..."
    
    # 检查Qt安装路径
    if [[ -n "${QT_PATH}" && -d "${QT_PATH}" ]]; then
        QMAKE_PATH="${QT_PATH}/bin/qmake"
        echo_success "使用指定的Qt路径: ${QT_PATH}"
    elif command -v qmake &> /dev/null; then
        QMAKE_PATH="qmake"
        QT_PATH=$(qmake -query QT_INSTALL_PREFIX)
        echo_success "使用系统Qt路径: ${QT_PATH}"
    else
        echo_error "未找到Qt环境"
        echo_info "请设置QT_PATH环境变量或安装Qt5开发包"
        echo_info "Ubuntu/Debian: sudo apt install qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
        exit 1
    fi
    
    # 检查qmake可执行性
    if [[ ! -x "${QMAKE_PATH}" ]]; then
        echo_error "qmake不可执行: ${QMAKE_PATH}"
        exit 1
    fi
    
    # 显示Qt版本信息
    QT_VERSION=$(${QMAKE_PATH} --version | grep "Qt version" | cut -d' ' -f4)
    echo_info "Qt版本: ${QT_VERSION}"
    
    # 检查必要的Qt模块
    QT_MODULES=("Core" "Gui" "Widgets" "Multimedia" "MultimediaWidgets")
    for module in "${QT_MODULES[@]}"; do
        MODULE_PATH="${QT_PATH}/lib/libQt5${module}.so"
        if [[ -f "${MODULE_PATH}" ]]; then
            echo_success "✓ Qt${module}模块可用"
        else
            echo_warning "⚠️ Qt${module}模块可能不可用"
        fi
    done
}

# 创建构建目录
setup_build_directory() {
    echo_info "设置构建目录..."
    
    # 创建构建目录
    mkdir -p "${BUILD_DIR}"
    
    # 创建必要的子目录
    mkdir -p "${BUILD_DIR}/obj"
    mkdir -p "${BUILD_DIR}/moc"
    mkdir -p "${BUILD_DIR}/rcc"
    mkdir -p "${BUILD_DIR}/ui"
    
    # 创建运行时目录
    mkdir -p "${BUILD_DIR}/data"
    mkdir -p "${BUILD_DIR}/models"
    mkdir -p "${BUILD_DIR}/images"
    mkdir -p "${BUILD_DIR}/temp"
    mkdir -p "${BUILD_DIR}/logs"
    mkdir -p "${BUILD_DIR}/config"
    
    echo_success "构建目录设置完成"
}

# 复制资源文件
copy_resources() {
    echo_info "复制资源文件..."
    
    # 复制配置文件
    if [[ -d "${SCRIPT_DIR}/config" ]]; then
        cp -r "${SCRIPT_DIR}/config/"* "${BUILD_DIR}/config/"
        echo_success "✓ 配置文件已复制"
    fi
    
    # 复制图标和图片资源（如果存在）
    if [[ -d "${SCRIPT_DIR}/icons" ]]; then
        cp -r "${SCRIPT_DIR}/icons" "${BUILD_DIR}/"
        echo_success "✓ 图标文件已复制"
    fi
    
    if [[ -d "${SCRIPT_DIR}/images" ]]; then
        cp -r "${SCRIPT_DIR}/images" "${BUILD_DIR}/"
        echo_success "✓ 图片文件已复制"
    fi
    
    echo_success "资源文件复制完成"
}

# 编译项目
compile_project() {
    echo_info "编译项目..."
    
    cd "${BUILD_DIR}"
    
    # 设置Qt环境变量
    export PATH="${QT_PATH}/bin:${PATH}"
    export LD_LIBRARY_PATH="${QT_PATH}/lib:${LD_LIBRARY_PATH}"
    export QT_PLUGIN_PATH="${QT_PATH}/plugins"
    
    # 生成Makefile
    echo_info "生成Makefile..."
    ${QMAKE_PATH} "${SCRIPT_DIR}/${PROJECT_NAME}.pro"
    
    if [[ $? -ne 0 ]]; then
        echo_error "qmake失败"
        exit 1
    fi
    
    echo_success "✓ Makefile生成成功"
    
    # 编译项目
    echo_info "编译源代码..."
    make -j$(nproc)
    
    if [[ $? -ne 0 ]]; then
        echo_error "编译失败"
        exit 1
    fi
    
    echo_success "✓ 编译成功"
    
    cd "${SCRIPT_DIR}"
}

# 检查编译结果
check_build_result() {
    echo_info "检查编译结果..."
    
    EXECUTABLE="${BUILD_DIR}/${PROJECT_NAME}"
    
    if [[ -f "${EXECUTABLE}" ]]; then
        echo_success "✓ 可执行文件生成成功: ${EXECUTABLE}"
        
        # 检查文件大小
        FILE_SIZE=$(du -h "${EXECUTABLE}" | cut -f1)
        echo_info "可执行文件大小: ${FILE_SIZE}"
        
        # 检查依赖库
        echo_info "检查依赖库..."
        if command -v ldd &> /dev/null; then
            MISSING_LIBS=$(ldd "${EXECUTABLE}" 2>/dev/null | grep "not found" || true)
            if [[ -n "${MISSING_LIBS}" ]]; then
                echo_warning "⚠️ 发现缺失的依赖库:"
                echo "${MISSING_LIBS}"
            else
                echo_success "✓ 所有依赖库都可用"
            fi
        fi
        
        # 设置执行权限
        chmod +x "${EXECUTABLE}"
        echo_success "✓ 执行权限已设置"
        
    else
        echo_error "可执行文件生成失败"
        exit 1
    fi
}

# 创建启动脚本
create_launch_script() {
    echo_info "创建启动脚本..."
    
    LAUNCH_SCRIPT="${BUILD_DIR}/run_diagnosis_app.sh"
    
    cat > "${LAUNCH_SCRIPT}" << EOF
#!/bin/bash
# 药盒识别与用药建议应用启动脚本

# 设置工作目录
cd "\$(dirname "\${BASH_SOURCE[0]}")"

# 设置Qt环境变量
export LD_LIBRARY_PATH="${QT_PATH}/lib:\${LD_LIBRARY_PATH}"
export QT_PLUGIN_PATH="${QT_PATH}/plugins"

# 设置字体环境变量
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

# 启动应用程序
echo "启动药盒识别与用药建议系统..."
./${PROJECT_NAME}
EOF
    
    chmod +x "${LAUNCH_SCRIPT}"
    echo_success "✓ 启动脚本已创建: ${LAUNCH_SCRIPT}"
}

# 显示使用说明
show_usage_info() {
    echo_info "=== 编译完成 ==="
    echo_success "🎉 药盒识别与用药建议应用编译成功！"
    
    echo_info "=== 使用说明 ==="
    echo_info "1. 直接运行:"
    echo_info "   cd ${BUILD_DIR} && ./${PROJECT_NAME}"
    echo_info ""
    echo_info "2. 使用启动脚本:"
    echo_info "   ${BUILD_DIR}/run_diagnosis_app.sh"
    echo_info ""
    echo_info "3. 设置环境变量后运行:"
    echo_info "   export LD_LIBRARY_PATH=${QT_PATH}/lib:\$LD_LIBRARY_PATH"
    echo_info "   ${BUILD_DIR}/${PROJECT_NAME}"
    
    echo_info "=== 文件位置 ==="
    echo_info "可执行文件: ${BUILD_DIR}/${PROJECT_NAME}"
    echo_info "配置文件: ${BUILD_DIR}/config/"
    echo_info "日志目录: ${BUILD_DIR}/logs/"
    echo_info "启动脚本: ${BUILD_DIR}/run_diagnosis_app.sh"
}

# 主函数
main() {
    echo_info "开始编译药盒识别与用药建议应用..."
    
    # 执行编译步骤
    check_qt_environment
    setup_build_directory
    copy_resources
    compile_project
    check_build_result
    create_launch_script
    show_usage_info
    
    echo_success "编译脚本执行完成！"
}

# 执行主函数
main "$@"
