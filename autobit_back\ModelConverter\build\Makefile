#############################################################################
# Makefile for building: ModelConverter
# Generated by qmake (3.1) (Qt 5.12.6)
# Project:  ../ModelConverter.pro
# Template: app
# Command: /media/rhs/t161/qt512/5.12.6/gcc_64/bin/qmake -o Makefile ../ModelConverter.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DSF_CORE_LS_EXPORT=Q_DECL_IMPORT -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../../ModelConverter -I. -I../../../qt512/5.12.6/gcc_64/include -I../../../qt512/5.12.6/gcc_64/include/QtWidgets -I../../../qt512/5.12.6/gcc_64/include/QtGui -I../../../qt512/5.12.6/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I../../../qt512/5.12.6/gcc_64/mkspecs/linux-g++
QMAKE         = /media/rhs/t161/qt512/5.12.6/gcc_64/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /media/rhs/t161/qt512/5.12.6/gcc_64/bin/qmake -install qinstall
QINSTALL_PROGRAM = /media/rhs/t161/qt512/5.12.6/gcc_64/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = ModelConverter1.0.0
DISTDIR = /media/rhs/t161/autobit/ModelConverter/build/.tmp/ModelConverter1.0.0
LINK          = g++
LFLAGS        = -Wl,-O1 -Wl,-rpath,/media/rhs/t161/qt512/5.12.6/gcc_64/lib -Wl,-rpath,/media/rhs/t161/autobit/ModelConverter -Wl,-rpath,/media/rhs/t161/qt512/5.12.6/gcc_64/lib
LIBS          = $(SUBLIBS) -L/media/rhs/t161/qt512/5.12.6/gcc_64/lib -lQt5Widgets -L/home/<USER>/openssl-1.1.1b/lib -lQt5Gui -lQt5Core -L/media/rhs/t161/autobit/ModelConverter -lsf-core-ls /media/rhs/t161/qt512/5.12.6/gcc_64/lib/libQt5Widgets.so /media/rhs/t161/qt512/5.12.6/gcc_64/lib/libQt5Gui.so /media/rhs/t161/qt512/5.12.6/gcc_64/lib/libQt5Core.so -lGL -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../main.cpp \
		../activationwindow.cpp \
		../conversionwindow.cpp \
		../sized_delete.cpp qrc_ModelConverter.cpp \
		moc_activationwindow.cpp \
		moc_conversionwindow.cpp
OBJECTS       = main.o \
		activationwindow.o \
		conversionwindow.o \
		sized_delete.o \
		qrc_ModelConverter.o \
		moc_activationwindow.o \
		moc_conversionwindow.o
DIST          = ../../../../qt512/5.12.6/gcc_64/mkspecs/features/spec_pre.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/unix.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/linux.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/sanitize.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/gcc-base.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/g++-base.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/common/g++-unix.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/qconfig.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/qt_functions.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/qt_config.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/spec_post.prf \
		../../.qmake.stash \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/toolchain.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/default_pre.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/resolve_config.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/default_post.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/warn_on.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/qt.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/resources.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/moc.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/uic.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/unix/thread.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/qmake_use.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/file_copies.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/exceptions.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/yacc.prf \
		../../../../qt512/5.12.6/gcc_64/mkspecs/features/lex.prf \
		../../ModelConverter.pro ../activationwindow.h \
		../conversionwindow.h ../main.cpp \
		../activationwindow.cpp \
		../conversionwindow.cpp \
		../sized_delete.cpp
QMAKE_TARGET  = ModelConverter
DESTDIR       = 
TARGET        = ModelConverter


first: all
####### Build rules

ModelConverter:  $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../ModelConverter.pro ../../../qt512/5.12.6/gcc_64/mkspecs/features/spec_pre.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/unix.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/linux.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/sanitize.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/gcc-base.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/g++-base.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/common/g++-unix.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/qconfig.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/qt_functions.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/qt_config.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/spec_post.prf \
		../.qmake.stash \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/toolchain.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/default_pre.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/resolve_config.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/default_post.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/warn_on.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/qt.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/resources.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/moc.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/uic.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/unix/thread.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/qmake_use.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/file_copies.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/exceptions.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/yacc.prf \
		../../../qt512/5.12.6/gcc_64/mkspecs/features/lex.prf \
		../ModelConverter.pro \
		../ModelConverter.qrc \
		../../../qt512/5.12.6/gcc_64/lib/libQt5Widgets.prl \
		../../../qt512/5.12.6/gcc_64/lib/libQt5Gui.prl \
		../../../qt512/5.12.6/gcc_64/lib/libQt5Core.prl
	$(QMAKE) -o Makefile ../ModelConverter.pro
../../../qt512/5.12.6/gcc_64/mkspecs/features/spec_pre.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/unix.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/linux.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/sanitize.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/gcc-base.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/gcc-base-unix.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/g++-base.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/common/g++-unix.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/qconfig.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_charts_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_core.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_core_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designer_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_gui_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_help.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_help_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_location.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_location_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_network.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_network_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qml_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quick_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_script.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_script_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_sql_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_svg_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_waylandcompositor_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_webview_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xml_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
../../../qt512/5.12.6/gcc_64/mkspecs/features/qt_functions.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/qt_config.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/linux-g++/qmake.conf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/spec_post.prf:
../.qmake.stash:
../../../qt512/5.12.6/gcc_64/mkspecs/features/exclusive_builds.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/toolchain.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/default_pre.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/resolve_config.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/default_post.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/warn_on.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/qt.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/resources.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/moc.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/unix/opengl.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/uic.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/unix/thread.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/qmake_use.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/file_copies.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/testcase_targets.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/exceptions.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/yacc.prf:
../../../qt512/5.12.6/gcc_64/mkspecs/features/lex.prf:
../ModelConverter.pro:
../ModelConverter.qrc:
../../../qt512/5.12.6/gcc_64/lib/libQt5Widgets.prl:
../../../qt512/5.12.6/gcc_64/lib/libQt5Gui.prl:
../../../qt512/5.12.6/gcc_64/lib/libQt5Core.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ../ModelConverter.pro

qmake_all: FORCE


all: Makefile ModelConverter

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../ModelConverter.qrc $(DISTDIR)/
	$(COPY_FILE) --parents ../../../qt512/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../activationwindow.h ../conversionwindow.h $(DISTDIR)/
	$(COPY_FILE) --parents ../main.cpp ../activationwindow.cpp ../conversionwindow.cpp ../sized_delete.cpp $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_ModelConverter.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_ModelConverter.cpp
qrc_ModelConverter.cpp: ../ModelConverter.qrc \
		../../../qt512/5.12.6/gcc_64/bin/rcc \
		../icons/error.png \
		../icons/working.png \
		../icons/ready.png \
		../icons/success.png
	/media/rhs/t161/qt512/5.12.6/gcc_64/bin/rcc -name ModelConverter ../ModelConverter.qrc -o qrc_ModelConverter.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: ../../../qt512/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp
	g++ -pipe -fno-sized-deallocation -O2 -std=gnu++1z -Wall -W -dM -E -o moc_predefs.h ../../../qt512/5.12.6/gcc_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_activationwindow.cpp moc_conversionwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_activationwindow.cpp moc_conversionwindow.cpp
moc_activationwindow.cpp: ../activationwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QWidget \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlogging.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qflags.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmutex.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstring.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qchar.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringview.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiterator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpair.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregexp.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmargins.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrect.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsize.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpoint.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpalette.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcolor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgb.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qbrush.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvector.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qregion.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qline.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtransform.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qimage.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhash.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfont.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvariant.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdebug.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlocale.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qset.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfile.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpen.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qicon.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLabel \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		../conversionwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QProcess \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocess.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		../SFCoreIntf.h \
		moc_predefs.h \
		../../../qt512/5.12.6/gcc_64/bin/moc
	/media/rhs/t161/qt512/5.12.6/gcc_64/bin/moc $(DEFINES) --include /media/rhs/t161/autobit/ModelConverter/build/moc_predefs.h -I/media/rhs/t161/qt512/5.12.6/gcc_64/mkspecs/linux-g++ -I/media/rhs/t161/autobit/ModelConverter -I/media/rhs/t161/qt512/5.12.6/gcc_64/include -I/media/rhs/t161/qt512/5.12.6/gcc_64/include/QtWidgets -I/media/rhs/t161/qt512/5.12.6/gcc_64/include/QtGui -I/media/rhs/t161/qt512/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../activationwindow.h -o moc_activationwindow.cpp

moc_conversionwindow.cpp: ../conversionwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QWidget \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlogging.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qflags.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmutex.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstring.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qchar.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringview.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiterator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpair.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregexp.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmargins.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrect.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsize.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpoint.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpalette.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcolor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgb.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qbrush.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvector.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qregion.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qline.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtransform.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qimage.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhash.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfont.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvariant.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdebug.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlocale.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qset.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfile.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QProcess \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocess.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qicon.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpen.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLabel \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		moc_predefs.h \
		../../../qt512/5.12.6/gcc_64/bin/moc
	/media/rhs/t161/qt512/5.12.6/gcc_64/bin/moc $(DEFINES) --include /media/rhs/t161/autobit/ModelConverter/build/moc_predefs.h -I/media/rhs/t161/qt512/5.12.6/gcc_64/mkspecs/linux-g++ -I/media/rhs/t161/autobit/ModelConverter -I/media/rhs/t161/qt512/5.12.6/gcc_64/include -I/media/rhs/t161/qt512/5.12.6/gcc_64/include/QtWidgets -I/media/rhs/t161/qt512/5.12.6/gcc_64/include/QtGui -I/media/rhs/t161/qt512/5.12.6/gcc_64/include/QtCore -I. -I/usr/include/c++/9 -I/usr/include/x86_64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/x86_64-linux-gnu/9/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../conversionwindow.h -o moc_conversionwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean 

####### Compile

main.o: ../main.cpp ../../../qt512/5.12.6/gcc_64/include/QtWidgets/QApplication \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qapplication.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlogging.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qflags.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmutex.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstring.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qchar.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringview.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiterator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpair.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregexp.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qeventloop.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpoint.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsize.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qdesktopwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmargins.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrect.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpalette.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcolor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgb.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qbrush.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvector.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qregion.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qline.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtransform.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qimage.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhash.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfont.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvariant.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdebug.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlocale.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qset.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfile.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qguiapplication.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qinputmethod.h \
		../activationwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QWidget \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpen.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qicon.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLabel \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		../conversionwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QProcess \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocess.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		../SFCoreIntf.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../main.cpp

activationwindow.o: ../activationwindow.cpp ../activationwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QWidget \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlogging.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qflags.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmutex.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstring.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qchar.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringview.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiterator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpair.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregexp.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmargins.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrect.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsize.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpoint.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpalette.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcolor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgb.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qbrush.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvector.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qregion.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qline.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtransform.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qimage.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhash.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfont.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvariant.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdebug.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlocale.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qset.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfile.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpen.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qicon.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLabel \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		../conversionwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QProcess \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocess.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		../SFCoreIntf.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QMessageBox \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		../YXPermission.h \
		../yxcppsdkdishcore_global.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o activationwindow.o ../activationwindow.cpp

conversionwindow.o: ../conversionwindow.cpp ../conversionwindow.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QWidget \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtguiglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobal.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qconfig.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtcore-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsystemdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocessordetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtypeinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsysinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlogging.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qflags.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbasicatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qgenericatomic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qglobalstatic.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmutex.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnumeric.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qversiontagging.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtgui-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qnamespace.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstring.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qchar.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrefcount.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qarraydata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringliteral.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringview.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringbuilder.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qalgorithms.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiterator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhashfunctions.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpair.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qbytearraylist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringlist.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregexp.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstringmatcher.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qscopedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmetatype.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qobject_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmargins.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpaintdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qrect.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsize.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qpoint.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpalette.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcolor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgb.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qrgba64.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qbrush.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvector.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qmatrix.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpolygon.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qregion.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdatastream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qiodevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qline.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtransform.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpainterpath.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qimage.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixelformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpixmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qshareddata.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qhash.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfont.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontmetrics.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qfontinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qkeysequence.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qevent.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qvariant.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qmap.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdebug.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qtextstream.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qlocale.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qset.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurl.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qurlquery.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfile.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfiledevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvector2d.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtouchdevice.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QProcess \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qprocess.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QComboBox \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qcombobox.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qvalidator.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qregularexpression.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qicon.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qstyle.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabbar.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qrubberband.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qframe.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLineEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlineedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextcursor.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextformat.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qpen.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextoption.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QPushButton \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qpushbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractbutton.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QTextEdit \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qtextedit.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qtextdocument.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QLabel \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlabel.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QHBoxLayout \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qboxlayout.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlayout.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qlayoutitem.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qgridlayout.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QVBoxLayout \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/QFileDialog \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qfiledialog.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qdir.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qfileinfo.h \
		../../../qt512/5.12.6/gcc_64/include/QtWidgets/qdialog.h \
		../../../qt512/5.12.6/gcc_64/include/QtGui/QDesktopServices \
		../../../qt512/5.12.6/gcc_64/include/QtGui/qdesktopservices.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qstandardpaths.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QDir \
		../../../qt512/5.12.6/gcc_64/include/QtCore/QCoreApplication \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qcoreapplication.h \
		../../../qt512/5.12.6/gcc_64/include/QtCore/qeventloop.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o conversionwindow.o ../conversionwindow.cpp

sized_delete.o: ../sized_delete.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o sized_delete.o ../sized_delete.cpp

qrc_ModelConverter.o: qrc_ModelConverter.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_ModelConverter.o qrc_ModelConverter.cpp

moc_activationwindow.o: moc_activationwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_activationwindow.o moc_activationwindow.cpp

moc_conversionwindow.o: moc_conversionwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_conversionwindow.o moc_conversionwindow.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

