#include "taskpage.h"
#include <QApplication>
#include <QMimeData>
#include <QImageReader>
#include <QStandardPaths>
#include <QDateTime>
#include <QMessageBox>

// 常量定义已在头文件中声明，这里不需要重复定义

TaskPage::TaskPage(QWidget *parent)
    : QWidget(parent)
    , m_currentQuestionIndex(0)
    , m_mainLayout(nullptr)
    , m_cameraWidget(nullptr)
    , m_modelInference(nullptr)
    , m_hasImage(false)
    , m_isInferencing(false)
    , m_progressTimer(new QTimer(this))
    , m_progressValue(0)
    , m_choicesButtonGroup(new QButtonGroup(this))
{
    setupUI();
    connectSignals();

    // 设置拖拽支持
    setAcceptDrops(true);

    qDebug() << "TaskPage created";
}

TaskPage::~TaskPage()
{
    if (m_progressTimer) {
        m_progressTimer->stop();
    }
    qDebug() << "TaskPage destroyed";
}

void TaskPage::setupUI()
{
    // 主布局 - 水平布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(15);

    // 设置各个区域
    setupOperationSection();
    setupQuestionSection();
    setupImageSection();
    setupInferenceSection();

    // 初始化组件
    m_cameraWidget = new CameraWidget(this);
    m_modelInference = new ModelInference(this);

    // 设置样式
    setStyleSheet(R"(
        QFrame {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
        QPushButton:pressed {
            background-color: #004085;
        }
        QPushButton:disabled {
            background-color: #6c757d;
        }
        QLabel#titleLabel {
            font-size: 16px;
            font-weight: bold;
            color: #212529;
        }
        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
        }
    )");
}

void TaskPage::setupOperationSection()
{
    // 左侧操作指引区域
    m_operationFrame = new QFrame();
    m_operationFrame->setFixedWidth(250);
    m_operationLayout = new QVBoxLayout(m_operationFrame);
    m_operationLayout->setContentsMargins(15, 15, 15, 15);
    m_operationLayout->setSpacing(10);

    // 标题
    m_operationTitle = new QLabel("操作事项");
    m_operationTitle->setObjectName("titleLabel");
    m_operationTitle->setAlignment(Qt::AlignCenter);
    m_operationLayout->addWidget(m_operationTitle);

    // 操作步骤
    m_step1Label = new QLabel("步骤1: 根据题目要求拍摄图像头下摆放指定药盒");
    m_step1Label->setWordWrap(true);
    m_operationLayout->addWidget(m_step1Label);

    m_step2Label = new QLabel("步骤2: 确认摆放正确后点击拍照按钮进行拍照");
    m_step2Label->setWordWrap(true);
    m_operationLayout->addWidget(m_step2Label);

    m_step3Label = new QLabel("步骤3: 确认图片无误后点击模型推理进行模型调用");
    m_step3Label->setWordWrap(true);
    m_operationLayout->addWidget(m_step3Label);

    m_step4Label = new QLabel("步骤4: 确认模型输出后对界面进行截屏，请确保截图中含题目、图像及模型输出内容");
    m_step4Label->setWordWrap(true);
    m_operationLayout->addWidget(m_step4Label);

    m_step5Label = new QLabel("步骤5: 确认每道题目都回答完毕后退出应用");
    m_step5Label->setWordWrap(true);
    m_operationLayout->addWidget(m_step5Label);

    m_operationLayout->addStretch();
    m_mainLayout->addWidget(m_operationFrame);
}

void TaskPage::setupQuestionSection()
{
    // 右侧主要区域
    m_rightFrame = new QFrame();
    m_rightLayout = new QVBoxLayout(m_rightFrame);
    m_rightLayout->setContentsMargins(15, 15, 15, 15);
    m_rightLayout->setSpacing(15);

    // 题目区域
    m_questionFrame = new QFrame();
    m_questionFrame->setMinimumHeight(200);
    m_questionLayout = new QVBoxLayout(m_questionFrame);
    m_questionLayout->setContentsMargins(15, 15, 15, 15);
    m_questionLayout->setSpacing(10);

    // 题目标题和导航
    m_questionHeaderLayout = new QHBoxLayout();
    m_questionTitleLabel = new QLabel("药盒：健胃消食片");
    m_questionTitleLabel->setObjectName("titleLabel");
    m_questionHeaderLayout->addWidget(m_questionTitleLabel);

    m_questionHeaderLayout->addStretch();

    // 导航按钮
    m_prevButton = new QPushButton("◀");
    m_prevButton->setFixedSize(30, 30);
    m_nextButton = new QPushButton("▶");
    m_nextButton->setFixedSize(30, 30);
    m_questionHeaderLayout->addWidget(m_prevButton);
    m_questionHeaderLayout->addWidget(m_nextButton);

    m_questionLayout->addLayout(m_questionHeaderLayout);

    // 题目内容
    m_questionTextLabel = new QLabel("题目1：图中是什么药品？只需返回正确的选项编号。");
    m_questionTextLabel->setWordWrap(true);
    m_questionLayout->addWidget(m_questionTextLabel);

    // 选择题选项区域
    m_choicesFrame = new QFrame();
    m_choicesLayout = new QVBoxLayout(m_choicesFrame);
    m_choicesLayout->setContentsMargins(10, 10, 10, 10);
    m_choicesLayout->setSpacing(8);
    m_questionLayout->addWidget(m_choicesFrame);

    m_questionLayout->addStretch();
    m_rightLayout->addWidget(m_questionFrame);

    m_mainLayout->addWidget(m_rightFrame, 1);
}

void TaskPage::setupImageSection()
{
    // 按钮区域
    m_buttonLayout = new QHBoxLayout();

    m_takePhotoButton = new QPushButton("拍照");
    m_takePhotoButton->setMinimumHeight(40);
    m_buttonLayout->addWidget(m_takePhotoButton);

    m_inferenceButton = new QPushButton("模型推理");
    m_inferenceButton->setMinimumHeight(40);
    m_buttonLayout->addWidget(m_inferenceButton);

    m_rightLayout->addLayout(m_buttonLayout);

    // 图片和推理结果区域
    m_contentLayout = new QHBoxLayout();

    // 图片预览区域
    m_imageFrame = new QFrame();
    m_imageFrame->setMinimumSize(300, 250);
    m_imageLayout = new QVBoxLayout(m_imageFrame);
    m_imageLayout->setContentsMargins(10, 10, 10, 10);

    m_imageAreaLabel = new QLabel("图片预览区");
    m_imageAreaLabel->setAlignment(Qt::AlignCenter);
    m_imageAreaLabel->setStyleSheet("font-weight: bold; color: #666;");
    m_imageLayout->addWidget(m_imageAreaLabel);

    m_imageLabel = new QLabel("点击拍照按钮获取图片");
    m_imageLabel->setAlignment(Qt::AlignCenter);
    m_imageLabel->setMinimumSize(280, 200);
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
    m_imageLayout->addWidget(m_imageLabel);

    m_contentLayout->addWidget(m_imageFrame);

    // 推理结果区域
    m_inferenceFrame = new QFrame();
    m_inferenceFrame->setMinimumSize(300, 250);
    m_inferenceLayout = new QVBoxLayout(m_inferenceFrame);
    m_inferenceLayout->setContentsMargins(10, 10, 10, 10);

    m_inferenceAreaLabel = new QLabel("模型输出区域");
    m_inferenceAreaLabel->setAlignment(Qt::AlignCenter);
    m_inferenceAreaLabel->setStyleSheet("font-weight: bold; color: #666;");
    m_inferenceLayout->addWidget(m_inferenceAreaLabel);

    m_inferenceResultEdit = new QTextEdit();
    m_inferenceResultEdit->setPlaceholderText("模型推理结果将在此显示");
    m_inferenceResultEdit->setMinimumHeight(200);
    m_inferenceLayout->addWidget(m_inferenceResultEdit);

    m_contentLayout->addWidget(m_inferenceFrame);

    m_rightLayout->addLayout(m_contentLayout);
}

void TaskPage::setupInferenceSection()
{
    // 测试按钮区域
    m_testFrame = new QFrame();
    m_testLayout = new QVBoxLayout(m_testFrame);
    m_testLayout->setContentsMargins(15, 15, 15, 15);
    m_testLayout->setSpacing(10);

    m_testLabel = new QLabel("模型测试");
    m_testLabel->setObjectName("titleLabel");
    m_testLabel->setAlignment(Qt::AlignCenter);
    m_testLayout->addWidget(m_testLabel);

    m_testHostButton = new QPushButton("主机模型测试");
    m_testHostButton->setMinimumHeight(40);
    m_testHostButton->setStyleSheet("QPushButton { background-color: #28a745; } QPushButton:hover { background-color: #218838; }");
    m_testLayout->addWidget(m_testHostButton);

    m_testRK3588Button = new QPushButton("RK3588模型测试");
    m_testRK3588Button->setMinimumHeight(40);
    m_testRK3588Button->setStyleSheet("QPushButton { background-color: #ffc107; color: #212529; } QPushButton:hover { background-color: #e0a800; }");
    m_testLayout->addWidget(m_testRK3588Button);

    m_testLayout->addStretch();

    // 进度条
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMinimumHeight(25);
    m_testLayout->addWidget(m_progressBar);

    m_progressLabel = new QLabel("准备就绪");
    m_progressLabel->setAlignment(Qt::AlignCenter);
    m_progressLabel->setStyleSheet("color: #666666;");
    m_testLayout->addWidget(m_progressLabel);

    m_rightLayout->addWidget(m_testFrame);
}

// 加载题目数据
void TaskPage::loadQuestions(const QString& jsonFilePath)
{
    QFile file(jsonFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, "错误", "无法打开题目文件: " + jsonFilePath);
        return;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (!doc.isArray()) {
        QMessageBox::warning(this, "错误", "题目文件格式错误");
        return;
    }

    QJsonArray questionsArray = doc.array();
    m_questions.clear();

    for (const auto& questionValue : questionsArray) {
        if (questionValue.isObject()) {
            QuestionData question = QuestionData::fromJson(questionValue.toObject());
            m_questions.append(question);
        }
    }

    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
        updateNavigationButtons();
    }
}

void TaskPage::connectSignals()
{
    // 按钮信号
    connect(m_takePhotoButton, &QPushButton::clicked, this, &TaskPage::onTakePhoto);
    connect(m_inferenceButton, &QPushButton::clicked, this, &TaskPage::onStartInference);
    connect(m_testHostButton, &QPushButton::clicked, this, &TaskPage::onTestHostModel);
    connect(m_testRK3588Button, &QPushButton::clicked, this, &TaskPage::onTestRK3588Model);

    // 导航按钮信号
    connect(m_prevButton, &QPushButton::clicked, this, &TaskPage::onPreviousQuestion);
    connect(m_nextButton, &QPushButton::clicked, this, &TaskPage::onNextQuestion);

    // 选择题答案信号
    connect(m_choicesButtonGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, &TaskPage::onAnswerSelected);

    // 模型推理信号
    connect(m_modelInference, &ModelInference::inferenceCompleted,
            this, &TaskPage::onInferenceCompleted);
    connect(m_modelInference, &ModelInference::inferenceError,
            this, &TaskPage::onInferenceError);

    // 相机信号
    connect(m_cameraWidget, &CameraWidget::imageCaptured,
            this, [this](const QString& imagePath) {
                loadImage(imagePath);
            });
}

void TaskPage::onTakePhoto()
{
    if (m_cameraWidget->isCameraAvailable()) {
        m_cameraWidget->startCamera();
        m_cameraWidget->captureImage();
    } else {
        emit taskError("摄像头不可用，请检查设备连接");
    }
}

void TaskPage::onStartInference()
{
    if (!m_hasImage) {
        QMessageBox::warning(this, "提示", "请先拍照获取图片");
        return;
    }

    callInferenceModel("question");
}

void TaskPage::onTestHostModel()
{
    if (!m_hasImage) {
        QMessageBox::warning(this, "提示", "请先拍照获取图片");
        return;
    }

    callInferenceModel("host");
}

void TaskPage::onTestRK3588Model()
{
    if (!m_hasImage) {
        QMessageBox::warning(this, "提示", "请先拍照获取图片");
        return;
    }

    callInferenceModel("rk3588");
}

void TaskPage::onNextQuestion()
{
    if (m_currentQuestionIndex < m_questions.size() - 1) {
        m_currentQuestionIndex++;
        displayCurrentQuestion();
        updateNavigationButtons();
    }
}

void TaskPage::onPreviousQuestion()
{
    if (m_currentQuestionIndex > 0) {
        m_currentQuestionIndex--;
        displayCurrentQuestion();
        updateNavigationButtons();
    }
}

void TaskPage::onAnswerSelected()
{
    QRadioButton* selectedButton = qobject_cast<QRadioButton*>(m_choicesButtonGroup->checkedButton());
    if (selectedButton) {
        QString answer = selectedButton->text().left(1); // 获取A、B、C、D
        emit questionAnswered(answer);
        qDebug() << "Answer selected:" << answer;
    }
}

void TaskPage::onInferenceCompleted(const InferenceResult& result)
{
    m_isInferencing = false;
    showInferenceProgress(false);

    if (result.success) {
        // 显示推理结果
        m_inferenceResultEdit->setPlainText(result.getFormattedInstructions());
    } else {
        m_inferenceResultEdit->setPlainText("推理失败: " + result.errorMessage);
    }

    emit inferenceFinished(result);
}

// 显示当前题目
void TaskPage::displayCurrentQuestion()
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        const QuestionData& question = m_questions[m_currentQuestionIndex];

        // 更新题目标题
        m_questionTitleLabel->setText(QString("药盒：健胃消食片"));

        // 更新题目内容
        m_questionTextLabel->setText(question.getQuestionText());

        // 清空之前的选项
        QLayoutItem* item;
        while ((item = m_choicesLayout->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }

        // 添加新的选项
        if (question.questionType == "choice") {
            QStringList choices = question.getChoices();
            for (const QString& choice : choices) {
                QRadioButton* radioButton = new QRadioButton(choice);
                m_choicesButtonGroup->addButton(radioButton);
                m_choicesLayout->addWidget(radioButton);
            }
        } else if (question.questionType == "judgment") {
            QRadioButton* trueButton = new QRadioButton("正确");
            QRadioButton* falseButton = new QRadioButton("错误");
            m_choicesButtonGroup->addButton(trueButton);
            m_choicesButtonGroup->addButton(falseButton);
            m_choicesLayout->addWidget(trueButton);
            m_choicesLayout->addWidget(falseButton);
        }
    }
}

// 更新导航按钮状态
void TaskPage::updateNavigationButtons()
{
    m_prevButton->setEnabled(m_currentQuestionIndex > 0);
    m_nextButton->setEnabled(m_currentQuestionIndex < m_questions.size() - 1);
}

void TaskPage::onInferenceError(const QString& errorMessage)
{
    m_isInferencing = false;
    showInferenceProgress(false);
    m_inferenceResultEdit->setPlainText("推理错误: " + errorMessage);
}

// 调用推理模型
void TaskPage::callInferenceModel(const QString& modelType)
{
    m_isInferencing = true;
    showInferenceProgress(true);

    QString modelPath;
    QString resultText;

    if (modelType == "host") {
        // 主机模型路径 - autobit_back/yolo-rk3588/models/
        modelPath = "../autobit_back/yolo-rk3588/models/yolov8n.onnx";
        resultText = "调用主机YOLO模型进行目标检测...";
        m_progressLabel->setText("正在使用主机模型推理...");
    } else if (modelType == "rk3588") {
        // RK3588模型路径 - autobit_back/yolo-rk3588/models/
        modelPath = "../autobit_back/yolo-rk3588/models/yolov8n.rknn";
        resultText = "调用RK3588 YOLO模型进行目标检测...";
        m_progressLabel->setText("正在使用RK3588模型推理...");
    } else {
        // 题目推理模型路径（默认）
        modelPath = "/path/to/question/model";
        resultText = "调用题目推理模型...";
        m_progressLabel->setText("正在进行题目推理...");
    }

    // 模拟推理过程
    QTimer::singleShot(2000, [this, modelType, modelPath]() {
        InferenceResult result;
        result.success = true;
        result.detectedMedicine = "健胃消食片";

        if (modelType == "host") {
            result.instructions = QString("主机模型推理结果:\n模型路径: %1\n检测到目标: 药盒\n置信度: 0.85\n边界框: [120, 80, 300, 200]").arg(modelPath);
        } else if (modelType == "rk3588") {
            result.instructions = QString("RK3588模型推理结果:\n模型路径: %1\n检测到目标: 药盒\n置信度: 0.82\n边界框: [115, 75, 305, 205]").arg(modelPath);
        } else {
            result.instructions = QString("题目推理结果:\n识别药品: 健胃消食片\n建议答案: B\n置信度: 0.92");
        }

        onInferenceCompleted(result);
    });
}

// 获取当前题目
QuestionData TaskPage::getCurrentQuestion() const
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        return m_questions[m_currentQuestionIndex];
    }
    return QuestionData();
}

void TaskPage::updateInferenceProgress()
{
    if (m_isInferencing && m_progressTimer) {
        m_progressValue = (m_progressValue + 5) % 100;
        m_progressBar->setValue(m_progressValue);
    }
}

void TaskPage::loadImage(const QString& imagePath)
{
    QImageReader reader(imagePath);
    if (!reader.canRead()) {
        emit taskError("无法读取图片文件: " + imagePath);
        return;
    }
    
    QImage image = reader.read();
    if (image.isNull()) {
        emit taskError("图片文件损坏或格式不支持");
        return;
    }
    
    // 缩放图片以适应显示区域
    QPixmap pixmap = QPixmap::fromImage(image);
    displayImage(pixmap);
    
    m_currentImagePath = imagePath;
    m_hasImage = true;
    
    QFileInfo fileInfo(imagePath);
    m_imageStatusLabel->setText(QString("已选择: %1 (%2)")
                               .arg(fileInfo.fileName())
                               .arg(fileInfo.size() > 1024*1024 ? 
                                    QString("%1 MB").arg(fileInfo.size()/1024.0/1024.0, 0, 'f', 1) :
                                    QString("%1 KB").arg(fileInfo.size()/1024.0, 0, 'f', 1)));
    
    emit imageUploaded(imagePath);
    
    qDebug() << "Image loaded:" << imagePath;
}

void TaskPage::displayImage(const QPixmap& pixmap)
{
    QPixmap scaledPixmap = pixmap.scaled(
        IMAGE_DISPLAY_WIDTH, IMAGE_DISPLAY_HEIGHT,
        Qt::KeepAspectRatio, Qt::SmoothTransformation
    );
    
    m_imageLabel->setPixmap(scaledPixmap);
    m_imageLabel->setStyleSheet("border: 2px solid #0078d4; border-radius: 8px;");
}

void TaskPage::showInferenceProgress(bool show)
{
    m_progressBar->setVisible(show);
    m_progressLabel->setVisible(show);
    m_inferenceButton->setEnabled(!show);
    
    if (show) {
        m_progressValue = 0;
        m_progressBar->setValue(0);
        m_progressLabel->setText("正在初始化推理...");
        
        // 启动进度更新定时器
        if (!m_progressTimer) {
            m_progressTimer = new QTimer(this);
            connect(m_progressTimer, &QTimer::timeout, this, &TaskPage::updateInferenceProgress);
        }
        m_progressTimer->start(200); // 每200ms更新一次
    } else {
        if (m_progressTimer) {
            m_progressTimer->stop();
        }
        m_progressBar->setValue(100);
        m_progressLabel->setText("推理完成");
    }
}

void TaskPage::resetPage()
{
    // 清空图片
    m_currentImagePath.clear();
    m_hasImage = false;
    m_imageLabel->clear();
    m_imageLabel->setText("点击拍照按钮获取图片");
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");

    // 清空推理结果
    m_inferenceResultEdit->clear();

    // 重置题目到第一题
    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
        updateNavigationButtons();
    }
}

void TaskPage::setPageEnabled(bool enabled)
{
    setEnabled(enabled);
}

void TaskPage::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        if (!urls.isEmpty()) {
            QString fileName = urls.first().toLocalFile();
            QStringList supportedFormats = {"png", "jpg", "jpeg", "bmp", "gif"};
            QString suffix = QFileInfo(fileName).suffix().toLower();
            
            if (supportedFormats.contains(suffix)) {
                event->acceptProposedAction();
                return;
            }
        }
    }
    event->ignore();
}

void TaskPage::dropEvent(QDropEvent *event)
{
    QList<QUrl> urls = event->mimeData()->urls();
    if (!urls.isEmpty()) {
        QString fileName = urls.first().toLocalFile();
        loadImage(fileName);
        event->acceptProposedAction();
    }
}
