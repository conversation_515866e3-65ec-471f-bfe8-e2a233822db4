#include "taskpage.h"
#include <QApplication>
#include <QMimeData>
#include <QImageReader>
#include <QStandardPaths>
#include <QDateTime>

const int TaskPage::IMAGE_DISPLAY_WIDTH = 400;
const int TaskPage::IMAGE_DISPLAY_HEIGHT = 300;
const int TaskPage::RESULT_IMAGE_WIDTH = 200;
const int TaskPage::RESULT_IMAGE_HEIGHT = 150;
const int TaskPage::FRAME_MARGIN = 15;
const int TaskPage::FRAME_SPACING = 10;

TaskPage::TaskPage(const TaskData& taskData, QWidget *parent)
    : QWidget(parent)
    , m_taskData(taskData)
    , m_mainLayout(nullptr)
    , m_scrollArea(nullptr)
    , m_contentWidget(nullptr)
    , m_contentLayout(nullptr)
    , m_guidanceFrame(nullptr)
    , m_guidanceLayout(nullptr)
    , m_title<PERSON>abel(nullptr)
    , m_guidanceLabel(nullptr)
    , m_imageFrame(nullptr)
    , m_imageLayout(nullptr)
    , m_imageButtonLayout(nullptr)
    , m_imageLabel(nullptr)
    , m_takePhotoButton(nullptr)
    , m_uploadButton(nullptr)
    , m_imageStatusLabel(nullptr)
    , m_symptomFrame(nullptr)
    , m_symptomLayout(nullptr)
    , m_symptomTitleLabel(nullptr)
    , m_symptomTextEdit(nullptr)
    , m_symptomHintLabel(nullptr)
    , m_inferenceFrame(nullptr)
    , m_inferenceLayout(nullptr)
    , m_inferenceButtonLayout(nullptr)
    , m_inferenceButton(nullptr)
    , m_clearButton(nullptr)
    , m_progressBar(nullptr)
    , m_progressLabel(nullptr)
    , m_resultFrame(nullptr)
    , m_resultLayout(nullptr)
    , m_resultContentLayout(nullptr)
    , m_resultImageLabel(nullptr)
    , m_resultTextEdit(nullptr)
    , m_resultTitleLabel(nullptr)
    , m_cameraWidget(nullptr)
    , m_modelInference(nullptr)
    , m_hasImage(false)
    , m_hasSymptom(false)
    , m_isInferencing(false)
    , m_progressTimer(nullptr)
    , m_progressValue(0)
{
    setupUI();
    connectSignals();
    
    // 设置拖拽支持
    setAcceptDrops(true);
    
    qDebug() << "TaskPage created for task:" << m_taskData.taskName;
}

TaskPage::~TaskPage()
{
    if (m_progressTimer) {
        m_progressTimer->stop();
    }
    qDebug() << "TaskPage destroyed for task:" << m_taskData.taskName;
}

void TaskPage::setupUI()
{
    // 主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    // 创建滚动区域
    m_scrollArea = new QScrollArea(this);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    
    m_contentWidget = new QWidget();
    m_contentLayout = new QVBoxLayout(m_contentWidget);
    m_contentLayout->setContentsMargins(FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN);
    m_contentLayout->setSpacing(FRAME_SPACING);
    
    // 设置各个区域
    setupGuidanceSection();
    setupImageSection();
    setupSymptomSection();
    setupInferenceSection();
    setupResultSection();
    
    m_scrollArea->setWidget(m_contentWidget);
    m_mainLayout->addWidget(m_scrollArea);
    
    // 初始化组件
    m_cameraWidget = new CameraWidget(this);
    m_modelInference = new ModelInference(this);
    
    // 初始状态
    validateInputs();
}

void TaskPage::setupGuidanceSection()
{
    m_guidanceFrame = new QFrame();
    m_guidanceFrame->setFrameStyle(QFrame::StyledPanel);
    m_guidanceLayout = new QVBoxLayout(m_guidanceFrame);
    m_guidanceLayout->setContentsMargins(FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN);
    m_guidanceLayout->setSpacing(10);
    
    // 标题
    m_titleLabel = new QLabel(m_taskData.taskName);
    m_titleLabel->setObjectName("titleLabel");
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_guidanceLayout->addWidget(m_titleLabel);
    
    // 指引文本
    m_guidanceLabel = new QLabel(m_taskData.guidanceText);
    m_guidanceLabel->setObjectName("guidanceLabel");
    m_guidanceLabel->setWordWrap(true);
    m_guidanceLabel->setAlignment(Qt::AlignLeft | Qt::AlignTop);
    m_guidanceLayout->addWidget(m_guidanceLabel);
    
    m_contentLayout->addWidget(m_guidanceFrame);
}

void TaskPage::setupImageSection()
{
    m_imageFrame = new QFrame();
    m_imageFrame->setObjectName("imageFrame");
    m_imageFrame->setFrameStyle(QFrame::StyledPanel);
    m_imageFrame->setMinimumHeight(IMAGE_DISPLAY_HEIGHT + 100);
    
    m_imageLayout = new QVBoxLayout(m_imageFrame);
    m_imageLayout->setContentsMargins(FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN);
    m_imageLayout->setSpacing(10);
    
    // 图片显示区域
    m_imageLabel = new QLabel("点击下方按钮拍照或上传图片\n支持拖拽图片到此区域");
    m_imageLabel->setAlignment(Qt::AlignCenter);
    m_imageLabel->setMinimumSize(IMAGE_DISPLAY_WIDTH, IMAGE_DISPLAY_HEIGHT);
    m_imageLabel->setStyleSheet("border: 2px dashed #555555; border-radius: 8px; color: #888888;");
    m_imageLayout->addWidget(m_imageLabel);
    
    // 按钮区域
    m_imageButtonLayout = new QHBoxLayout();
    
    m_takePhotoButton = new QPushButton("📷 拍照");
    m_takePhotoButton->setMinimumHeight(40);
    m_imageButtonLayout->addWidget(m_takePhotoButton);
    
    m_uploadButton = new QPushButton("📁 上传图片");
    m_uploadButton->setMinimumHeight(40);
    m_imageButtonLayout->addWidget(m_uploadButton);
    
    m_imageLayout->addLayout(m_imageButtonLayout);
    
    // 状态标签
    m_imageStatusLabel = new QLabel("未选择图片");
    m_imageStatusLabel->setAlignment(Qt::AlignCenter);
    m_imageStatusLabel->setStyleSheet("color: #888888;");
    m_imageLayout->addWidget(m_imageStatusLabel);
    
    m_contentLayout->addWidget(m_imageFrame);
}

void TaskPage::setupSymptomSection()
{
    m_symptomFrame = new QFrame();
    m_symptomFrame->setFrameStyle(QFrame::StyledPanel);
    
    m_symptomLayout = new QVBoxLayout(m_symptomFrame);
    m_symptomLayout->setContentsMargins(FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN);
    m_symptomLayout->setSpacing(10);
    
    // 标题
    m_symptomTitleLabel = new QLabel("症状描述");
    m_symptomTitleLabel->setObjectName("titleLabel");
    m_symptomLayout->addWidget(m_symptomTitleLabel);
    
    // 输入框
    m_symptomTextEdit = new QTextEdit();
    m_symptomTextEdit->setPlaceholderText(m_taskData.symptomPlaceholder);
    m_symptomTextEdit->setMinimumHeight(120);
    m_symptomTextEdit->setMaximumHeight(200);
    m_symptomLayout->addWidget(m_symptomTextEdit);
    
    // 提示标签
    m_symptomHintLabel = new QLabel("请详细描述症状，以便系统提供更准确的用药建议");
    m_symptomHintLabel->setStyleSheet("color: #888888; font-size: 11px;");
    m_symptomLayout->addWidget(m_symptomHintLabel);
    
    m_contentLayout->addWidget(m_symptomFrame);
}

void TaskPage::setupInferenceSection()
{
    m_inferenceFrame = new QFrame();
    m_inferenceFrame->setFrameStyle(QFrame::StyledPanel);
    
    m_inferenceLayout = new QVBoxLayout(m_inferenceFrame);
    m_inferenceLayout->setContentsMargins(FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN);
    m_inferenceLayout->setSpacing(10);
    
    // 按钮区域
    m_inferenceButtonLayout = new QHBoxLayout();
    
    m_inferenceButton = new QPushButton("🚀 开始推理");
    m_inferenceButton->setObjectName("primaryButton");
    m_inferenceButton->setMinimumHeight(50);
    m_inferenceButton->setEnabled(false);
    m_inferenceButtonLayout->addWidget(m_inferenceButton);
    
    m_clearButton = new QPushButton("🗑️ 清空重置");
    m_clearButton->setMinimumHeight(50);
    m_inferenceButtonLayout->addWidget(m_clearButton);
    
    m_inferenceLayout->addLayout(m_inferenceButtonLayout);
    
    // 进度条
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMinimum(0);
    m_progressBar->setMaximum(100);
    m_inferenceLayout->addWidget(m_progressBar);
    
    // 进度标签
    m_progressLabel = new QLabel();
    m_progressLabel->setVisible(false);
    m_progressLabel->setAlignment(Qt::AlignCenter);
    m_progressLabel->setStyleSheet("color: #0078d4;");
    m_inferenceLayout->addWidget(m_progressLabel);
    
    m_contentLayout->addWidget(m_inferenceFrame);
}

void TaskPage::setupResultSection()
{
    m_resultFrame = new QFrame();
    m_resultFrame->setObjectName("resultFrame");
    m_resultFrame->setFrameStyle(QFrame::StyledPanel);
    m_resultFrame->setVisible(false);
    
    m_resultLayout = new QVBoxLayout(m_resultFrame);
    m_resultLayout->setContentsMargins(FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN, FRAME_MARGIN);
    m_resultLayout->setSpacing(10);
    
    // 标题
    m_resultTitleLabel = new QLabel("推理结果");
    m_resultTitleLabel->setObjectName("titleLabel");
    m_resultLayout->addWidget(m_resultTitleLabel);
    
    // 内容区域
    m_resultContentLayout = new QHBoxLayout();
    
    // 结果图片
    m_resultImageLabel = new QLabel();
    m_resultImageLabel->setFixedSize(RESULT_IMAGE_WIDTH, RESULT_IMAGE_HEIGHT);
    m_resultImageLabel->setAlignment(Qt::AlignCenter);
    m_resultImageLabel->setStyleSheet("border: 1px solid #555555; border-radius: 4px;");
    m_resultContentLayout->addWidget(m_resultImageLabel);
    
    // 结果文本
    m_resultTextEdit = new QTextEdit();
    m_resultTextEdit->setReadOnly(true);
    m_resultTextEdit->setMinimumHeight(RESULT_IMAGE_HEIGHT);
    m_resultContentLayout->addWidget(m_resultTextEdit, 1);
    
    m_resultLayout->addLayout(m_resultContentLayout);
    m_contentLayout->addWidget(m_resultFrame);
}

void TaskPage::connectSignals()
{
    // 图片相关信号
    connect(m_takePhotoButton, &QPushButton::clicked, this, &TaskPage::onTakePhoto);
    connect(m_uploadButton, &QPushButton::clicked, this, &TaskPage::onUploadImage);
    
    // 推理相关信号
    connect(m_inferenceButton, &QPushButton::clicked, this, &TaskPage::onStartInference);
    connect(m_clearButton, &QPushButton::clicked, this, &TaskPage::onClearAll);
    
    // 症状输入信号
    connect(m_symptomTextEdit, &QTextEdit::textChanged, this, &TaskPage::validateInputs);
    
    // 模型推理信号
    connect(m_modelInference, &ModelInference::inferenceCompleted, 
            this, &TaskPage::onInferenceCompleted);
    connect(m_modelInference, &ModelInference::inferenceError, 
            this, &TaskPage::onInferenceError);
    connect(m_modelInference, &ModelInference::progressUpdated, 
            this, &TaskPage::updateInferenceProgress);
    
    // 相机信号
    connect(m_cameraWidget, &CameraWidget::imageCaptured, 
            this, [this](const QString& imagePath) {
                loadImage(imagePath);
            });
}

void TaskPage::onTakePhoto()
{
    if (m_cameraWidget->isCameraAvailable()) {
        m_cameraWidget->startCamera();
        m_cameraWidget->captureImage();
    } else {
        emit taskError("摄像头不可用，请检查设备连接");
    }
}

void TaskPage::onUploadImage()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择药盒图片",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)"
    );
    
    if (!fileName.isEmpty()) {
        loadImage(fileName);
    }
}

void TaskPage::onStartInference()
{
    if (!m_hasImage || m_symptomTextEdit->toPlainText().trimmed().isEmpty()) {
        emit taskError("请先上传图片并输入症状描述");
        return;
    }
    
    m_isInferencing = true;
    showInferenceProgress(true);
    
    QString symptomText = m_symptomTextEdit->toPlainText().trimmed();
    m_modelInference->startInference(m_currentImagePath, symptomText, m_taskData);
    
    emit inferenceStarted();
}

void TaskPage::onInferenceCompleted(const InferenceResult& result)
{
    m_isInferencing = false;
    showInferenceProgress(false);
    
    if (result.success) {
        displayResult(result);
        emit taskCompleted(m_taskData.taskId);
    } else {
        emit taskError(result.errorMessage);
    }
    
    emit inferenceFinished(result);
}

void TaskPage::onInferenceError(const QString& errorMessage)
{
    m_isInferencing = false;
    showInferenceProgress(false);
    emit taskError(errorMessage);
}

void TaskPage::onClearAll()
{
    // 清空图片
    m_currentImagePath.clear();
    m_hasImage = false;
    m_imageLabel->clear();
    m_imageLabel->setText("点击下方按钮拍照或上传图片\n支持拖拽图片到此区域");
    m_imageLabel->setStyleSheet("border: 2px dashed #555555; border-radius: 8px; color: #888888;");
    m_imageStatusLabel->setText("未选择图片");
    
    // 清空症状
    m_symptomTextEdit->clear();
    m_hasSymptom = false;
    
    // 隐藏结果
    m_resultFrame->setVisible(false);
    
    // 重新验证输入
    validateInputs();
    
    qDebug() << "Page cleared for task:" << m_taskData.taskName;
}

void TaskPage::updateInferenceProgress()
{
    if (m_isInferencing && m_progressTimer) {
        m_progressValue = (m_progressValue + 5) % 100;
        m_progressBar->setValue(m_progressValue);
        
        QStringList messages = {
            "正在分析图片...",
            "正在识别药盒...",
            "正在匹配症状...",
            "正在生成建议...",
            "即将完成..."
        };
        
        int messageIndex = (m_progressValue / 20) % messages.size();
        m_progressLabel->setText(messages[messageIndex]);
    }
}

void TaskPage::loadImage(const QString& imagePath)
{
    QImageReader reader(imagePath);
    if (!reader.canRead()) {
        emit taskError("无法读取图片文件: " + imagePath);
        return;
    }
    
    QImage image = reader.read();
    if (image.isNull()) {
        emit taskError("图片文件损坏或格式不支持");
        return;
    }
    
    // 缩放图片以适应显示区域
    QPixmap pixmap = QPixmap::fromImage(image);
    displayImage(pixmap);
    
    m_currentImagePath = imagePath;
    m_hasImage = true;
    
    QFileInfo fileInfo(imagePath);
    m_imageStatusLabel->setText(QString("已选择: %1 (%2)")
                               .arg(fileInfo.fileName())
                               .arg(fileInfo.size() > 1024*1024 ? 
                                    QString("%1 MB").arg(fileInfo.size()/1024.0/1024.0, 0, 'f', 1) :
                                    QString("%1 KB").arg(fileInfo.size()/1024.0, 0, 'f', 1)));
    
    validateInputs();
    emit imageUploaded(imagePath);
    
    qDebug() << "Image loaded:" << imagePath;
}

void TaskPage::displayImage(const QPixmap& pixmap)
{
    QPixmap scaledPixmap = pixmap.scaled(
        IMAGE_DISPLAY_WIDTH, IMAGE_DISPLAY_HEIGHT,
        Qt::KeepAspectRatio, Qt::SmoothTransformation
    );
    
    m_imageLabel->setPixmap(scaledPixmap);
    m_imageLabel->setStyleSheet("border: 2px solid #0078d4; border-radius: 8px;");
}

void TaskPage::showInferenceProgress(bool show)
{
    m_progressBar->setVisible(show);
    m_progressLabel->setVisible(show);
    m_inferenceButton->setEnabled(!show);
    
    if (show) {
        m_progressValue = 0;
        m_progressBar->setValue(0);
        m_progressLabel->setText("正在初始化推理...");
        
        // 启动进度更新定时器
        if (!m_progressTimer) {
            m_progressTimer = new QTimer(this);
            connect(m_progressTimer, &QTimer::timeout, this, &TaskPage::updateInferenceProgress);
        }
        m_progressTimer->start(200); // 每200ms更新一次
    } else {
        if (m_progressTimer) {
            m_progressTimer->stop();
        }
        m_progressBar->setValue(100);
        m_progressLabel->setText("推理完成");
    }
}

void TaskPage::displayResult(const InferenceResult& result)
{
    // 显示结果区域
    m_resultFrame->setVisible(true);
    
    // 显示结果图片
    if (!result.medicineImage.isEmpty() && QFile::exists(result.medicineImage)) {
        QPixmap resultPixmap(result.medicineImage);
        if (!resultPixmap.isNull()) {
            QPixmap scaledPixmap = resultPixmap.scaled(
                RESULT_IMAGE_WIDTH, RESULT_IMAGE_HEIGHT,
                Qt::KeepAspectRatio, Qt::SmoothTransformation
            );
            m_resultImageLabel->setPixmap(scaledPixmap);
        }
    } else {
        m_resultImageLabel->setText("无图片");
    }
    
    // 显示结果文本
    m_resultTextEdit->setHtml(QString(
        "<h3 style='color: #0078d4;'>%1</h3>"
        "<div style='line-height: 1.6;'>%2</div>"
    ).arg(result.detectedMedicine.isEmpty() ? "识别结果" : result.detectedMedicine)
     .arg(result.getFormattedInstructions().replace("\n", "<br>")));
    
    // 滚动到结果区域
    QTimer::singleShot(100, [this]() {
        m_scrollArea->ensureWidgetVisible(m_resultFrame);
    });
    
    qDebug() << "Result displayed for task:" << m_taskData.taskName;
}

void TaskPage::validateInputs()
{
    m_hasSymptom = !m_symptomTextEdit->toPlainText().trimmed().isEmpty();
    bool canInference = m_hasImage && m_hasSymptom && !m_isInferencing;
    m_inferenceButton->setEnabled(canInference);
}

void TaskPage::resetPage()
{
    onClearAll();
}

void TaskPage::setPageEnabled(bool enabled)
{
    setEnabled(enabled);
}

void TaskPage::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        if (!urls.isEmpty()) {
            QString fileName = urls.first().toLocalFile();
            QStringList supportedFormats = {"png", "jpg", "jpeg", "bmp", "gif"};
            QString suffix = QFileInfo(fileName).suffix().toLower();
            
            if (supportedFormats.contains(suffix)) {
                event->acceptProposedAction();
                return;
            }
        }
    }
    event->ignore();
}

void TaskPage::dropEvent(QDropEvent *event)
{
    QList<QUrl> urls = event->mimeData()->urls();
    if (!urls.isEmpty()) {
        QString fileName = urls.first().toLocalFile();
        loadImage(fileName);
        event->acceptProposedAction();
    }
}
