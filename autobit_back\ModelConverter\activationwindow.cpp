#include "activationwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include "SFCoreIntf.h"
#include "YXPermission.h"
#include <QDebug>

ActivationWindow::ActivationWindow(QWidget *parent)
    : QWidget(parent)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    QLabel *label = new QLabel(tr("请输入激活码："), this);
    licenseEdit = new QLineEdit(this);
    activateButton = new QPushButton(tr("激活"), this);
    statusLabel = new QLabel(this);

    statusLabel->setAlignment(Qt::AlignCenter);

    mainLayout->addWidget(label);
    mainLayout->addWidget(licenseEdit);
    mainLayout->addWidget(activateButton);
    mainLayout->addWidget(statusLabel);

    connect(activateButton, &QPushButton::clicked, this, &ActivationWindow::onActivateClicked);
}

void ActivationWindow::onActivateClicked()
{
    QString qLic = licenseEdit->text().trimmed();
    if (qLic.isEmpty()) {
        QMessageBox::warning(this, tr("错误"), tr("请输入激活码！"));
        return;
    }

    // 获取设备号并打印到控制台
    std::string deviceNo = ::getDeviceNo();
    qDebug() << "[激活] 当前设备号:" << QString::fromStdString(deviceNo);

    // 构造激活参数
    DeviceVM vm;
    vm.deviceNo = "50bf70582c5ea2ac7502656f8cfb522e";
    vm.productNo = "40201";
    vm.licenseNo = qLic.toStdString();

    // 调用服务端激活接口
    SF_LICENSE_STATE state = callServerToActivate(vm);
    std::string errMsg = getErrorMessage(state);

    if (state == SF_OK) {
        statusLabel->setText(tr("激活成功，正在打开转换窗口..."));
        convWindow = new ConversionWindow();
        convWindow->show();
        this->close();
    } else {
        statusLabel->setText(QString::fromLocal8Bit(errMsg.c_str()));
        QMessageBox::warning(this, tr("激活失败"), QString::fromLocal8Bit(errMsg.c_str()));
    }
}
