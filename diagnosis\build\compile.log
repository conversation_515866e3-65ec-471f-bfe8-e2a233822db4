g++ -c -pipe -fno-sized-deallocation -O2 -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DQT_DEPRECATED_WARNINGS -DRELEASE_MODE -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../diagnosis -I. -I../../diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -Ibuild/moc -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o build/obj/taskpage.o ../taskpage.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DQT_DEPRECATED_WARNINGS -DRELEASE_MODE -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../diagnosis -I. -I../../diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -Ibuild/moc -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o build/obj/taskmanager.o ../taskmanager.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DQT_DEPRECATED_WARNINGS -DRELEASE_MODE -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../diagnosis -I. -I../../diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -Ibuild/moc -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o build/obj/modelinference.o ../modelinference.cpp
g++ -c -pipe -fno-sized-deallocation -O2 -O2 -std=gnu++1z -Wall -W -D_REENTRANT -fPIC -DQT_DEPRECATED_WARNINGS -DRELEASE_MODE -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -I../../diagnosis -I. -I../../diagnosis -I/opt/Qt5.12.6/5.12.6/gcc_64/include -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimediaWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtMultimedia -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtNetwork -I/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore -Ibuild/moc -isystem /usr/include/libdrm -I/opt/Qt5.12.6/5.12.6/gcc_64/mkspecs/linux-g++ -o build/obj/camerawidget.o ../camerawidget.cpp
make: *** No rule to make target '../images/loading.gif', needed by 'build/rcc/qrc_DiagnosisApp.cpp'.  Stop.
make: *** Waiting for unfinished jobs....
../taskmanager.cpp:270:20: error: stray ‘\345’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                    ^
../taskmanager.cpp:270:21: error: stray ‘\274’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                     ^
../taskmanager.cpp:270:22: error: stray ‘\200’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                      ^
../taskmanager.cpp:270:23: error: stray ‘\345’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                       ^
../taskmanager.cpp:270:24: error: stray ‘\247’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                        ^
../taskmanager.cpp:270:25: error: stray ‘\213’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                         ^
../taskmanager.cpp:270:26: error: stray ‘\346’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                          ^
../taskmanager.cpp:270:27: error: stray ‘\216’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                           ^
../taskmanager.cpp:270:28: error: stray ‘\250’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                            ^
../taskmanager.cpp:270:29: error: stray ‘\347’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                             ^
../taskmanager.cpp:270:30: error: stray ‘\220’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                              ^
../taskmanager.cpp:270:31: error: stray ‘\206’ in program
  270 |         "3. 点击"开始推理"按钮获取用药建议\n\n"
      |                               ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h:46,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug:1,
                 from ../taskmanager.h:11,
                 from ../taskmanager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
../taskmanager.cpp: In member function ‘bool TaskManager::saveToFile(const QString&) const’:
../taskmanager.cpp:123:42: error: incomplete type ‘QDateTime’ used in nested name specifier
  123 |     rootObj["lastModified"] = QDateTime::currentDateTime().toString(Qt::ISODate);
      |                                          ^~~~~~~~~~~~~~~
../taskmanager.cpp: In member function ‘TaskData TaskManager::createDefaultTask(int, const QString&, const QString&) const’:
../taskmanager.cpp:279:98: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  279 |         task.expectedSymptoms = {"发热", "头痛", "咳嗽", "流鼻涕", "喉咙痛", "乏力"};
      |                                                                                                  ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../taskmanager.h:4,
                 from ../taskmanager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
../taskmanager.cpp:281:88: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  281 |         task.expectedSymptoms = {"头痛", "牙痛", "关节痛", "肌肉痛", "经痛"};
      |                                                                                        ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../taskmanager.h:4,
                 from ../taskmanager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
../taskmanager.cpp:283:82: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  283 |         task.expectedSymptoms = {"发炎", "红肿", "疼痛", "发热", "感染"};
      |                                                                                  ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../taskmanager.h:4,
                 from ../taskmanager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
../taskmanager.cpp:285:88: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  285 |         task.expectedSymptoms = {"胃痛", "胃胀", "消化不良", "胃酸", "恶心"};
      |                                                                                        ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../taskmanager.h:4,
                 from ../taskmanager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
../taskmanager.cpp:287:84: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  287 |         task.expectedSymptoms = {"疲劳", "免疫力低", "营养不良", "贫血"};
      |                                                                                    ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../taskmanager.h:4,
                 from ../taskmanager.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qtextstream.h:46,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qdebug.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QDebug:1,
                 from ../modelinference.h:13,
                 from ../modelinference.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlocale.h:43,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:44,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../camerawidget.h:9,
                 from ../camerawidget.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
make: *** [Makefile:2221: build/obj/taskmanager.o] Error 1
../modelinference.cpp: At global scope:
../modelinference.cpp:6:27: error: duplicate initialization of ‘ModelInference::DEFAULT_TIMEOUT_SECONDS’
    6 | const int ModelInference::DEFAULT_TIMEOUT_SECONDS = 30;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
../modelinference.cpp: In member function ‘void InferenceWorker::runInference(const QString&, const QString&, const QString&, const QString&)’:
../modelinference.cpp:62:40: error: incomplete type ‘QDateTime’ used in nested name specifier
   62 |                             QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".json";
      |                                        ^~~~~~~~~~~~~~~
../modelinference.cpp:65:77: error: ‘INFERENCE_EXECUTABLE’ was not declared in this scope
   65 |     QString executablePath = QCoreApplication::applicationDirPath() + "/" + INFERENCE_EXECUTABLE;
      |                                                                             ^~~~~~~~~~~~~~~~~~~~
../modelinference.cpp: In member function ‘QString InferenceWorker::createInputJson(const QString&, const QString&) const’:
../modelinference.cpp:153:39: error: incomplete type ‘QDateTime’ used in nested name specifier
  153 |                            QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".json";
      |                                       ^~~~~~~~~~~~~~~
../modelinference.cpp:158:40: error: incomplete type ‘QDateTime’ used in nested name specifier
  158 |     inputObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
      |                                        ^~~~~~~~~~~~~~~
../modelinference.cpp: In member function ‘InferenceResult InferenceWorker::createMockResult(const QString&, const QString&) const’:
../modelinference.cpp:228:94: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  228 |         result.warnings = {"服药期间不得饮酒", "不宜与其他解热镇痛药同用"};
      |                                                                                              ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../modelinference.h:4,
                 from ../modelinference.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
../modelinference.cpp:236:95: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  236 |         result.warnings = {"长期服用需定期检查", "与其他药物间隔2小时服用"};
      |                                                                                               ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../modelinference.h:4,
                 from ../modelinference.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
../modelinference.cpp:244:57: error: ambiguous overload for ‘operator=’ (operand types are ‘QStringList’ and ‘<brace-enclosed initializer list>’)
  244 |         result.warnings = {"不宜与碱性药物同服"};
      |                                                         ^
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qlist.h:1097,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qobject.h:49,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/QObject:1,
                 from ../modelinference.h:4,
                 from ../modelinference.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:113:18: note: candidate: ‘QStringList& QStringList::operator=(const QList<QString>&)’
  113 |     QStringList &operator=(const QList<QString> &other)
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:116:18: note: candidate: ‘QStringList& QStringList::operator=(QList<QString>&&)’
  116 |     QStringList &operator=(QList<QString> &&other) Q_DECL_NOTHROW
      |                  ^~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(const QStringList&)’
   99 | class QStringList : public QList<QString>
      |       ^~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qstringlist.h:99:7: note: candidate: ‘QStringList& QStringList::operator=(QStringList&&)’
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../camerawidget.h:9,
                 from ../camerawidget.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtGui/qtextdocument.h:47,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qtextedit.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QTextEdit:1,
                 from ../taskpage.h:10,
                 from ../taskpage.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h: In constructor ‘QVariant::QVariant(QVariant&&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:273:25: warning: implicitly-declared ‘constexpr QVariant::Private& QVariant::Private::operator=(const QVariant::Private&)’ is deprecated [-Wdeprecated-copy]
  273 |     { other.d = Private(); }
      |                         ^
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtCore/qvariant.h:399:16: note: because ‘QVariant::Private’ has user-provided ‘QVariant::Private::Private(const QVariant::Private&)’
  399 |         inline Private(const Private &other) Q_DECL_NOTHROW
      |                ^~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
../camerawidget.cpp: At global scope:
../camerawidget.cpp:3:25: error: duplicate initialization of ‘CameraWidget::VIEWFINDER_WIDTH’
    3 | const int CameraWidget::VIEWFINDER_WIDTH = 640;
      |                         ^~~~~~~~~~~~~~~~
../camerawidget.cpp:4:25: error: duplicate initialization of ‘CameraWidget::VIEWFINDER_HEIGHT’
    4 | const int CameraWidget::VIEWFINDER_HEIGHT = 480;
      |                         ^~~~~~~~~~~~~~~~~
../camerawidget.cpp: In member function ‘QString CameraWidget::createImageSavePath() const’:
../camerawidget.cpp:188:36: error: incomplete type ‘QDateTime’ used in nested name specifier
  188 |     QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
      |                                    ^~~~~~~~~~~~~~~
make: *** [Makefile:2318: build/obj/modelinference.o] Error 1
In file included from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qabstractitemdelegate.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qcombobox.h:45,
                 from /opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/QComboBox:1,
                 from ../camerawidget.h:9,
                 from ../taskpage.h:26,
                 from ../taskpage.cpp:1:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:103: warning: implicitly-declared ‘QStyleOptionFocusRect& QStyleOptionFocusRect::operator=(const QStyleOptionFocusRect&)’ is deprecated [-Wdeprecated-copy]
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                       ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:120:5: note: because ‘QStyleOptionFocusRect’ has user-provided ‘QStyleOptionFocusRect::QStyleOptionFocusRect(const QStyleOptionFocusRect&)’
  120 |     QStyleOptionFocusRect(const QStyleOptionFocusRect &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:95: warning: implicitly-declared ‘QStyleOptionFrame& QStyleOptionFrame::operator=(const QStyleOptionFrame&)’ is deprecated [-Wdeprecated-copy]
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                               ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:144:5: note: because ‘QStyleOptionFrame’ has user-provided ‘QStyleOptionFrame::QStyleOptionFrame(const QStyleOptionFrame&)’
  144 |     QStyleOptionFrame(const QStyleOptionFrame &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:173:49: warning: implicitly-declared ‘QStyleOptionTabWidgetFrame& QStyleOptionTabWidgetFrame::operator=(const QStyleOptionTabWidgetFrame&)’ is deprecated [-Wdeprecated-copy]
  173 |         : QStyleOption(Version, Type) { *this = other; }
      |                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:172:12: note: because ‘QStyleOptionTabWidgetFrame’ has user-provided ‘QStyleOptionTabWidgetFrame::QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame&)’
  172 |     inline QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other)
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:105: warning: implicitly-declared ‘QStyleOptionTabBarBase& QStyleOptionTabBarBase::operator=(const QStyleOptionTabBarBase&)’ is deprecated [-Wdeprecated-copy]
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:196:5: note: because ‘QStyleOptionTabBarBase’ has user-provided ‘QStyleOptionTabBarBase::QStyleOptionTabBarBase(const QStyleOptionTabBarBase&)’
  196 |     QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:97: warning: implicitly-declared ‘QStyleOptionHeader& QStyleOptionHeader::operator=(const QStyleOptionHeader&)’ is deprecated [-Wdeprecated-copy]
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:227:5: note: because ‘QStyleOptionHeader’ has user-provided ‘QStyleOptionHeader::QStyleOptionHeader(const QStyleOptionHeader&)’
  227 |     QStyleOptionHeader(const QStyleOptionHeader &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:97: warning: implicitly-declared ‘QStyleOptionButton& QStyleOptionButton::operator=(const QStyleOptionButton&)’ is deprecated [-Wdeprecated-copy]
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                 ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:249:5: note: because ‘QStyleOptionButton’ has user-provided ‘QStyleOptionButton::QStyleOptionButton(const QStyleOptionButton&)’
  249 |     QStyleOptionButton(const QStyleOptionButton &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:91: warning: implicitly-declared ‘QStyleOptionTab& QStyleOptionTab::operator=(const QStyleOptionTab&)’ is deprecated [-Wdeprecated-copy]
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:286:5: note: because ‘QStyleOptionTab’ has user-provided ‘QStyleOptionTab::QStyleOptionTab(const QStyleOptionTab&)’
  286 |     QStyleOptionTab(const QStyleOptionTab &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:99: warning: implicitly-declared ‘QStyleOptionToolBar& QStyleOptionToolBar::operator=(const QStyleOptionToolBar&)’ is deprecated [-Wdeprecated-copy]
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:316:5: note: because ‘QStyleOptionToolBar’ has user-provided ‘QStyleOptionToolBar::QStyleOptionToolBar(const QStyleOptionToolBar&)’
  316 |     QStyleOptionToolBar(const QStyleOptionToolBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:107: warning: implicitly-declared ‘QStyleOptionProgressBar& QStyleOptionProgressBar::operator=(const QStyleOptionProgressBar&)’ is deprecated [-Wdeprecated-copy]
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                           ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:343:5: note: because ‘QStyleOptionProgressBar’ has user-provided ‘QStyleOptionProgressBar::QStyleOptionProgressBar(const QStyleOptionProgressBar&)’
  343 |     QStyleOptionProgressBar(const QStyleOptionProgressBar &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:101: warning: implicitly-declared ‘QStyleOptionMenuItem& QStyleOptionMenuItem::operator=(const QStyleOptionMenuItem&)’ is deprecated [-Wdeprecated-copy]
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:373:5: note: because ‘QStyleOptionMenuItem’ has user-provided ‘QStyleOptionMenuItem::QStyleOptionMenuItem(const QStyleOptionMenuItem&)’
  373 |     QStyleOptionMenuItem(const QStyleOptionMenuItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:105: warning: implicitly-declared ‘QStyleOptionDockWidget& QStyleOptionDockWidget::operator=(const QStyleOptionDockWidget&)’ is deprecated [-Wdeprecated-copy]
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:392:5: note: because ‘QStyleOptionDockWidget’ has user-provided ‘QStyleOptionDockWidget::QStyleOptionDockWidget(const QStyleOptionDockWidget&)’
  392 |     QStyleOptionDockWidget(const QStyleOptionDockWidget &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:101: warning: implicitly-declared ‘QStyleOptionViewItem& QStyleOptionViewItem::operator=(const QStyleOptionViewItem&)’ is deprecated [-Wdeprecated-copy]
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                     ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:443:5: note: because ‘QStyleOptionViewItem’ has user-provided ‘QStyleOptionViewItem::QStyleOptionViewItem(const QStyleOptionViewItem&)’
  443 |     QStyleOptionViewItem(const QStyleOptionViewItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:99: warning: implicitly-declared ‘QStyleOptionToolBox& QStyleOptionToolBox::operator=(const QStyleOptionToolBox&)’ is deprecated [-Wdeprecated-copy]
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:473:5: note: because ‘QStyleOptionToolBox’ has user-provided ‘QStyleOptionToolBox::QStyleOptionToolBox(const QStyleOptionToolBox&)’
  473 |     QStyleOptionToolBox(const QStyleOptionToolBox &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:105: warning: implicitly-declared ‘QStyleOptionRubberBand& QStyleOptionRubberBand::operator=(const QStyleOptionRubberBand&)’ is deprecated [-Wdeprecated-copy]
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                         ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:492:5: note: because ‘QStyleOptionRubberBand’ has user-provided ‘QStyleOptionRubberBand::QStyleOptionRubberBand(const QStyleOptionRubberBand&)’
  492 |     QStyleOptionRubberBand(const QStyleOptionRubberBand &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:99: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                   ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: warning: implicitly-declared ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ is deprecated [-Wdeprecated-copy]
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:5: note: because ‘QStyleOptionSlider’ has user-provided ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:514:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  514 | class Q_WIDGETS_EXPORT QStyleOptionSlider : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSlider::QStyleOptionSlider(const QStyleOptionSlider&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:534:104: note: synthesized method ‘QStyleOptionSlider& QStyleOptionSlider::operator=(const QStyleOptionSlider&)’ first required here
  534 |     QStyleOptionSlider(const QStyleOptionSlider &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                        ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: warning: implicitly-declared ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ is deprecated [-Wdeprecated-copy]
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:5: note: because ‘QStyleOptionSpinBox’ has user-provided ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:542:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  542 | class Q_WIDGETS_EXPORT QStyleOptionSpinBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSpinBox::QStyleOptionSpinBox(const QStyleOptionSpinBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:553:106: note: synthesized method ‘QStyleOptionSpinBox& QStyleOptionSpinBox::operator=(const QStyleOptionSpinBox&)’ first required here
  553 |     QStyleOptionSpinBox(const QStyleOptionSpinBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                          ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: warning: implicitly-declared ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ is deprecated [-Wdeprecated-copy]
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:5: note: because ‘QStyleOptionToolButton’ has user-provided ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:560:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  560 | class Q_WIDGETS_EXPORT QStyleOptionToolButton : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionToolButton::QStyleOptionToolButton(const QStyleOptionToolButton&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:580:112: note: synthesized method ‘QStyleOptionToolButton& QStyleOptionToolButton::operator=(const QStyleOptionToolButton&)’ first required here
  580 |     QStyleOptionToolButton(const QStyleOptionToolButton &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                                ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: warning: implicitly-declared ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ is deprecated [-Wdeprecated-copy]
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:5: note: because ‘QStyleOptionComboBox’ has user-provided ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:588:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  588 | class Q_WIDGETS_EXPORT QStyleOptionComboBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionComboBox::QStyleOptionComboBox(const QStyleOptionComboBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:602:108: note: synthesized method ‘QStyleOptionComboBox& QStyleOptionComboBox::operator=(const QStyleOptionComboBox&)’ first required here
  602 |     QStyleOptionComboBox(const QStyleOptionComboBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: warning: implicitly-declared ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ is deprecated [-Wdeprecated-copy]
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:5: note: because ‘QStyleOptionTitleBar’ has user-provided ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:608:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  608 | class Q_WIDGETS_EXPORT QStyleOptionTitleBar : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionTitleBar::QStyleOptionTitleBar(const QStyleOptionTitleBar&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:620:108: note: synthesized method ‘QStyleOptionTitleBar& QStyleOptionTitleBar::operator=(const QStyleOptionTitleBar&)’ first required here
  620 |     QStyleOptionTitleBar(const QStyleOptionTitleBar &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: warning: implicitly-declared ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ is deprecated [-Wdeprecated-copy]
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:5: note: because ‘QStyleOptionGroupBox’ has user-provided ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:626:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  626 | class Q_WIDGETS_EXPORT QStyleOptionGroupBox : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGroupBox::QStyleOptionGroupBox(const QStyleOptionGroupBox&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:640:108: note: synthesized method ‘QStyleOptionGroupBox& QStyleOptionGroupBox::operator=(const QStyleOptionGroupBox&)’ first required here
  640 |     QStyleOptionGroupBox(const QStyleOptionGroupBox &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: warning: implicitly-declared ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ is deprecated [-Wdeprecated-copy]
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:5: note: because ‘QStyleOptionSizeGrip’ has user-provided ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In member function ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:645:24: warning: implicitly-declared ‘QStyleOptionComplex& QStyleOptionComplex::operator=(const QStyleOptionComplex&)’ is deprecated [-Wdeprecated-copy]
  645 | class Q_WIDGETS_EXPORT QStyleOptionSizeGrip : public QStyleOptionComplex
      |                        ^~~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:510:5: note: because ‘QStyleOptionComplex’ has user-provided ‘QStyleOptionComplex::QStyleOptionComplex(const QStyleOptionComplex&)’
  510 |     QStyleOptionComplex(const QStyleOptionComplex &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionSizeGrip::QStyleOptionSizeGrip(const QStyleOptionSizeGrip&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:654:108: note: synthesized method ‘QStyleOptionSizeGrip& QStyleOptionSizeGrip::operator=(const QStyleOptionSizeGrip&)’ first required here
  654 |     QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other) : QStyleOptionComplex(Version, Type) { *this = other; }
      |                                                                                                            ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h: In copy constructor ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’:
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:109: warning: implicitly-declared ‘QStyleOptionGraphicsItem& QStyleOptionGraphicsItem::operator=(const QStyleOptionGraphicsItem&)’ is deprecated [-Wdeprecated-copy]
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |                                                                                                             ^~~~~
/opt/Qt5.12.6/5.12.6/gcc_64/include/QtWidgets/qstyleoption.h:670:5: note: because ‘QStyleOptionGraphicsItem’ has user-provided ‘QStyleOptionGraphicsItem::QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem&)’
  670 |     QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other) : QStyleOption(Version, Type) { *this = other; }
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
make: *** [Makefile:2491: build/obj/camerawidget.o] Error 1
../taskpage.cpp: At global scope:
../taskpage.cpp:8:21: error: duplicate initialization of ‘TaskPage::IMAGE_DISPLAY_WIDTH’
    8 | const int TaskPage::IMAGE_DISPLAY_WIDTH = 400;
      |                     ^~~~~~~~~~~~~~~~~~~
../taskpage.cpp:9:21: error: duplicate initialization of ‘TaskPage::IMAGE_DISPLAY_HEIGHT’
    9 | const int TaskPage::IMAGE_DISPLAY_HEIGHT = 300;
      |                     ^~~~~~~~~~~~~~~~~~~~
../taskpage.cpp:10:21: error: duplicate initialization of ‘TaskPage::RESULT_IMAGE_WIDTH’
   10 | const int TaskPage::RESULT_IMAGE_WIDTH = 200;
      |                     ^~~~~~~~~~~~~~~~~~
../taskpage.cpp:11:21: error: duplicate initialization of ‘TaskPage::RESULT_IMAGE_HEIGHT’
   11 | const int TaskPage::RESULT_IMAGE_HEIGHT = 150;
      |                     ^~~~~~~~~~~~~~~~~~~
../taskpage.cpp:12:21: error: duplicate initialization of ‘TaskPage::FRAME_MARGIN’
   12 | const int TaskPage::FRAME_MARGIN = 15;
      |                     ^~~~~~~~~~~~
../taskpage.cpp:13:21: error: duplicate initialization of ‘TaskPage::FRAME_SPACING’
   13 | const int TaskPage::FRAME_SPACING = 10;
      |                     ^~~~~~~~~~~~~
make: *** [Makefile:2135: build/obj/taskpage.o] Error 1
