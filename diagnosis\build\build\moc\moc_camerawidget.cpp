/****************************************************************************
** Meta object code from reading C++ file 'camerawidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.6)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../camerawidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'camerawidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.6. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CameraWidget_t {
    QByteArrayData data[26];
    char stringdata0[336];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CameraWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CameraWidget_t qt_meta_stringdata_CameraWidget = {
    {
QT_MOC_LITERAL(0, 0, 12), // "CameraWidget"
QT_MOC_LITERAL(1, 13, 13), // "imageCaptured"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 9), // "imagePath"
QT_MOC_LITERAL(4, 38, 11), // "cameraError"
QT_MOC_LITERAL(5, 50, 12), // "errorMessage"
QT_MOC_LITERAL(6, 63, 18), // "cameraStateChanged"
QT_MOC_LITERAL(7, 82, 8), // "isActive"
QT_MOC_LITERAL(8, 91, 20), // "onCameraStateChanged"
QT_MOC_LITERAL(9, 112, 14), // "QCamera::State"
QT_MOC_LITERAL(10, 127, 5), // "state"
QT_MOC_LITERAL(11, 133, 13), // "onCameraError"
QT_MOC_LITERAL(12, 147, 14), // "QCamera::Error"
QT_MOC_LITERAL(13, 162, 5), // "error"
QT_MOC_LITERAL(14, 168, 15), // "onImageCaptured"
QT_MOC_LITERAL(15, 184, 2), // "id"
QT_MOC_LITERAL(16, 187, 5), // "image"
QT_MOC_LITERAL(17, 193, 12), // "onImageSaved"
QT_MOC_LITERAL(18, 206, 8), // "fileName"
QT_MOC_LITERAL(19, 215, 14), // "onCaptureError"
QT_MOC_LITERAL(20, 230, 26), // "QCameraImageCapture::Error"
QT_MOC_LITERAL(21, 257, 11), // "errorString"
QT_MOC_LITERAL(22, 269, 18), // "onStartStopClicked"
QT_MOC_LITERAL(23, 288, 16), // "onCaptureClicked"
QT_MOC_LITERAL(24, 305, 24), // "onCameraSelectionChanged"
QT_MOC_LITERAL(25, 330, 5) // "index"

    },
    "CameraWidget\0imageCaptured\0\0imagePath\0"
    "cameraError\0errorMessage\0cameraStateChanged\0"
    "isActive\0onCameraStateChanged\0"
    "QCamera::State\0state\0onCameraError\0"
    "QCamera::Error\0error\0onImageCaptured\0"
    "id\0image\0onImageSaved\0fileName\0"
    "onCaptureError\0QCameraImageCapture::Error\0"
    "errorString\0onStartStopClicked\0"
    "onCaptureClicked\0onCameraSelectionChanged\0"
    "index"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CameraWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   69,    2, 0x06 /* Public */,
       4,    1,   72,    2, 0x06 /* Public */,
       6,    1,   75,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    1,   78,    2, 0x08 /* Private */,
      11,    1,   81,    2, 0x08 /* Private */,
      14,    2,   84,    2, 0x08 /* Private */,
      17,    2,   89,    2, 0x08 /* Private */,
      19,    3,   94,    2, 0x08 /* Private */,
      22,    0,  101,    2, 0x08 /* Private */,
      23,    0,  102,    2, 0x08 /* Private */,
      24,    1,  103,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::Bool,    7,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void, 0x80000000 | 12,   13,
    QMetaType::Void, QMetaType::Int, QMetaType::QImage,   15,   16,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,   15,   18,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 20, QMetaType::QString,   15,   13,   21,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   25,

       0        // eod
};

void CameraWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CameraWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->imageCaptured((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->cameraError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->cameraStateChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->onCameraStateChanged((*reinterpret_cast< QCamera::State(*)>(_a[1]))); break;
        case 4: _t->onCameraError((*reinterpret_cast< QCamera::Error(*)>(_a[1]))); break;
        case 5: _t->onImageCaptured((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QImage(*)>(_a[2]))); break;
        case 6: _t->onImageSaved((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 7: _t->onCaptureError((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QCameraImageCapture::Error(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 8: _t->onStartStopClicked(); break;
        case 9: _t->onCaptureClicked(); break;
        case 10: _t->onCameraSelectionChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCamera::State >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCamera::Error >(); break;
            }
            break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCameraImageCapture::Error >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CameraWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraWidget::imageCaptured)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CameraWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraWidget::cameraError)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CameraWidget::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraWidget::cameraStateChanged)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CameraWidget::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_CameraWidget.data,
    qt_meta_data_CameraWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CameraWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CameraWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CameraWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int CameraWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void CameraWidget::imageCaptured(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CameraWidget::cameraError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CameraWidget::cameraStateChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
