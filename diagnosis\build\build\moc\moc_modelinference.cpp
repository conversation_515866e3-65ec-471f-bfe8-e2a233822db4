/****************************************************************************
** Meta object code from reading C++ file 'modelinference.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.6)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../modelinference.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'modelinference.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.6. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_InferenceWorker_t {
    QByteArrayData data[14];
    char stringdata0[170];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_InferenceWorker_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_InferenceWorker_t qt_meta_stringdata_InferenceWorker = {
    {
QT_MOC_LITERAL(0, 0, 15), // "InferenceWorker"
QT_MOC_LITERAL(1, 16, 18), // "inferenceCompleted"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 15), // "InferenceResult"
QT_MOC_LITERAL(4, 52, 6), // "result"
QT_MOC_LITERAL(5, 59, 14), // "inferenceError"
QT_MOC_LITERAL(6, 74, 12), // "errorMessage"
QT_MOC_LITERAL(7, 87, 15), // "progressUpdated"
QT_MOC_LITERAL(8, 103, 10), // "percentage"
QT_MOC_LITERAL(9, 114, 12), // "runInference"
QT_MOC_LITERAL(10, 127, 9), // "imagePath"
QT_MOC_LITERAL(11, 137, 11), // "symptomText"
QT_MOC_LITERAL(12, 149, 9), // "modelPath"
QT_MOC_LITERAL(13, 159, 10) // "configPath"

    },
    "InferenceWorker\0inferenceCompleted\0\0"
    "InferenceResult\0result\0inferenceError\0"
    "errorMessage\0progressUpdated\0percentage\0"
    "runInference\0imagePath\0symptomText\0"
    "modelPath\0configPath"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_InferenceWorker[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       5,    1,   37,    2, 0x06 /* Public */,
       7,    1,   40,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       9,    4,   43,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::Int,    8,

 // slots: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::QString,   10,   11,   12,   13,

       0        // eod
};

void InferenceWorker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<InferenceWorker *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->inferenceCompleted((*reinterpret_cast< const InferenceResult(*)>(_a[1]))); break;
        case 1: _t->inferenceError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->progressUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 3: _t->runInference((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< const QString(*)>(_a[4]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (InferenceWorker::*)(const InferenceResult & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InferenceWorker::inferenceCompleted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (InferenceWorker::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InferenceWorker::inferenceError)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (InferenceWorker::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&InferenceWorker::progressUpdated)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject InferenceWorker::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_InferenceWorker.data,
    qt_meta_data_InferenceWorker,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *InferenceWorker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *InferenceWorker::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_InferenceWorker.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int InferenceWorker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void InferenceWorker::inferenceCompleted(const InferenceResult & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void InferenceWorker::inferenceError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void InferenceWorker::progressUpdated(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_ModelInference_t {
    QByteArrayData data[15];
    char stringdata0[212];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ModelInference_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ModelInference_t qt_meta_stringdata_ModelInference = {
    {
QT_MOC_LITERAL(0, 0, 14), // "ModelInference"
QT_MOC_LITERAL(1, 15, 16), // "inferenceStarted"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 18), // "inferenceCompleted"
QT_MOC_LITERAL(4, 52, 15), // "InferenceResult"
QT_MOC_LITERAL(5, 68, 6), // "result"
QT_MOC_LITERAL(6, 75, 14), // "inferenceError"
QT_MOC_LITERAL(7, 90, 12), // "errorMessage"
QT_MOC_LITERAL(8, 103, 15), // "progressUpdated"
QT_MOC_LITERAL(9, 119, 10), // "percentage"
QT_MOC_LITERAL(10, 130, 15), // "timeoutOccurred"
QT_MOC_LITERAL(11, 146, 20), // "onInferenceCompleted"
QT_MOC_LITERAL(12, 167, 16), // "onInferenceError"
QT_MOC_LITERAL(13, 184, 17), // "onProgressUpdated"
QT_MOC_LITERAL(14, 202, 9) // "onTimeout"

    },
    "ModelInference\0inferenceStarted\0\0"
    "inferenceCompleted\0InferenceResult\0"
    "result\0inferenceError\0errorMessage\0"
    "progressUpdated\0percentage\0timeoutOccurred\0"
    "onInferenceCompleted\0onInferenceError\0"
    "onProgressUpdated\0onTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ModelInference[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   59,    2, 0x06 /* Public */,
       3,    1,   60,    2, 0x06 /* Public */,
       6,    1,   63,    2, 0x06 /* Public */,
       8,    1,   66,    2, 0x06 /* Public */,
      10,    0,   69,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      11,    1,   70,    2, 0x08 /* Private */,
      12,    1,   73,    2, 0x08 /* Private */,
      13,    1,   76,    2, 0x08 /* Private */,
      14,    0,   79,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 4,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::Int,    9,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 4,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::Int,    9,
    QMetaType::Void,

       0        // eod
};

void ModelInference::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ModelInference *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->inferenceStarted(); break;
        case 1: _t->inferenceCompleted((*reinterpret_cast< const InferenceResult(*)>(_a[1]))); break;
        case 2: _t->inferenceError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->progressUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 4: _t->timeoutOccurred(); break;
        case 5: _t->onInferenceCompleted((*reinterpret_cast< const InferenceResult(*)>(_a[1]))); break;
        case 6: _t->onInferenceError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->onProgressUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 8: _t->onTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ModelInference::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModelInference::inferenceStarted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ModelInference::*)(const InferenceResult & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModelInference::inferenceCompleted)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ModelInference::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModelInference::inferenceError)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ModelInference::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModelInference::progressUpdated)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ModelInference::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModelInference::timeoutOccurred)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ModelInference::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_ModelInference.data,
    qt_meta_data_ModelInference,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ModelInference::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ModelInference::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ModelInference.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ModelInference::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void ModelInference::inferenceStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ModelInference::inferenceCompleted(const InferenceResult & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ModelInference::inferenceError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ModelInference::progressUpdated(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ModelInference::timeoutOccurred()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
