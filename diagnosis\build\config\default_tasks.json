{
    "version": "1.0",
    "lastModified": "2024-12-01T10:00:00",
    "tasks": [
        {
            "taskId": 1,
            "taskName": "感冒药识别",
            "guidanceText": "欢迎使用感冒药识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的感冒药包装照片\n2. 在下方输入您的症状描述\n3. 点击"开始推理"按钮获取用药建议\n\n注意：请确保照片清晰，药盒包装完整可见。",
            "symptomPlaceholder": "请详细描述您的感冒症状，例如：发热38.5°C、头痛、咳嗽有痰、流鼻涕等...",
            "medicineType": "感冒药",
            "expectedSymptoms": ["发热", "头痛", "咳嗽", "流鼻涕", "喉咙痛", "乏力", "鼻塞", "打喷嚏"],
            "modelPath": "models/cold_medicine_model.rknn",
            "configPath": "config/cold_medicine_config.json",
            "isActive": true
        },
        {
            "taskId": 2,
            "taskName": "止痛药识别",
            "guidanceText": "欢迎使用止痛药识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的止痛药包装照片\n2. 在下方输入您的疼痛症状描述\n3. 点击"开始推理"按钮获取用药建议\n\n注意：请确保照片清晰，药盒包装完整可见。",
            "symptomPlaceholder": "请详细描述您的疼痛症状，例如：头痛剧烈、牙痛、关节痛、肌肉酸痛等...",
            "medicineType": "止痛药",
            "expectedSymptoms": ["头痛", "牙痛", "关节痛", "肌肉痛", "经痛", "腰痛", "神经痛"],
            "modelPath": "models/painkiller_model.rknn",
            "configPath": "config/painkiller_config.json",
            "isActive": true
        },
        {
            "taskId": 3,
            "taskName": "消炎药识别",
            "guidanceText": "欢迎使用消炎药识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的消炎药包装照片\n2. 在下方输入您的炎症症状描述\n3. 点击"开始推理"按钮获取用药建议\n\n注意：请确保照片清晰，药盒包装完整可见。",
            "symptomPlaceholder": "请详细描述您的炎症症状，例如：伤口发炎、红肿热痛、感染发热等...",
            "medicineType": "消炎药",
            "expectedSymptoms": ["发炎", "红肿", "疼痛", "发热", "感染", "化脓", "伤口愈合慢"],
            "modelPath": "models/antiinflammatory_model.rknn",
            "configPath": "config/antiinflammatory_config.json",
            "isActive": true
        },
        {
            "taskId": 4,
            "taskName": "胃药识别",
            "guidanceText": "欢迎使用胃药识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的胃药包装照片\n2. 在下方输入您的胃部症状描述\n3. 点击"开始推理"按钮获取用药建议\n\n注意：请确保照片清晰，药盒包装完整可见。",
            "symptomPlaceholder": "请详细描述您的胃部症状，例如：胃痛、胃胀、消化不良、胃酸过多、恶心呕吐等...",
            "medicineType": "胃药",
            "expectedSymptoms": ["胃痛", "胃胀", "消化不良", "胃酸", "恶心", "呕吐", "胃灼热", "食欲不振"],
            "modelPath": "models/stomach_medicine_model.rknn",
            "configPath": "config/stomach_medicine_config.json",
            "isActive": true
        },
        {
            "taskId": 5,
            "taskName": "维生素识别",
            "guidanceText": "欢迎使用维生素识别功能！\n\n请按照以下步骤操作：\n1. 拍摄或上传清晰的维生素包装照片\n2. 在下方输入您的身体状况描述\n3. 点击"开始推理"按钮获取补充建议\n\n注意：请确保照片清晰，药盒包装完整可见。",
            "symptomPlaceholder": "请详细描述您的身体状况，例如：疲劳乏力、免疫力低下、贫血、营养不良等...",
            "medicineType": "维生素",
            "expectedSymptoms": ["疲劳", "免疫力低", "营养不良", "贫血", "皮肤干燥", "记忆力下降", "食欲不振"],
            "modelPath": "models/vitamin_model.rknn",
            "configPath": "config/vitamin_config.json",
            "isActive": true
        }
    ]
}
