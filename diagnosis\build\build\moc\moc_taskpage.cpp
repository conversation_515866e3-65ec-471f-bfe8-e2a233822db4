/****************************************************************************
** Meta object code from reading C++ file 'taskpage.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.6)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../taskpage.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'taskpage.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.6. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_TaskPage_t {
    QByteArrayData data[23];
    char stringdata0[329];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TaskPage_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TaskPage_t qt_meta_stringdata_TaskPage = {
    {
QT_MOC_LITERAL(0, 0, 8), // "TaskPage"
QT_MOC_LITERAL(1, 9, 16), // "questionAnswered"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 6), // "answer"
QT_MOC_LITERAL(4, 34, 9), // "taskError"
QT_MOC_LITERAL(5, 44, 12), // "errorMessage"
QT_MOC_LITERAL(6, 57, 13), // "imageUploaded"
QT_MOC_LITERAL(7, 71, 9), // "imagePath"
QT_MOC_LITERAL(8, 81, 16), // "inferenceStarted"
QT_MOC_LITERAL(9, 98, 17), // "inferenceFinished"
QT_MOC_LITERAL(10, 116, 15), // "InferenceResult"
QT_MOC_LITERAL(11, 132, 6), // "result"
QT_MOC_LITERAL(12, 139, 11), // "onTakePhoto"
QT_MOC_LITERAL(13, 151, 13), // "onUploadImage"
QT_MOC_LITERAL(14, 165, 16), // "onStartInference"
QT_MOC_LITERAL(15, 182, 15), // "onTestHostModel"
QT_MOC_LITERAL(16, 198, 17), // "onTestRK3588Model"
QT_MOC_LITERAL(17, 216, 20), // "onInferenceCompleted"
QT_MOC_LITERAL(18, 237, 16), // "onInferenceError"
QT_MOC_LITERAL(19, 254, 14), // "onNextQuestion"
QT_MOC_LITERAL(20, 269, 18), // "onPreviousQuestion"
QT_MOC_LITERAL(21, 288, 16), // "onAnswerSelected"
QT_MOC_LITERAL(22, 305, 23) // "updateInferenceProgress"

    },
    "TaskPage\0questionAnswered\0\0answer\0"
    "taskError\0errorMessage\0imageUploaded\0"
    "imagePath\0inferenceStarted\0inferenceFinished\0"
    "InferenceResult\0result\0onTakePhoto\0"
    "onUploadImage\0onStartInference\0"
    "onTestHostModel\0onTestRK3588Model\0"
    "onInferenceCompleted\0onInferenceError\0"
    "onNextQuestion\0onPreviousQuestion\0"
    "onAnswerSelected\0updateInferenceProgress"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TaskPage[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   94,    2, 0x06 /* Public */,
       4,    1,   97,    2, 0x06 /* Public */,
       6,    1,  100,    2, 0x06 /* Public */,
       8,    0,  103,    2, 0x06 /* Public */,
       9,    1,  104,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    0,  107,    2, 0x08 /* Private */,
      13,    0,  108,    2, 0x08 /* Private */,
      14,    0,  109,    2, 0x08 /* Private */,
      15,    0,  110,    2, 0x08 /* Private */,
      16,    0,  111,    2, 0x08 /* Private */,
      17,    1,  112,    2, 0x08 /* Private */,
      18,    1,  115,    2, 0x08 /* Private */,
      19,    0,  118,    2, 0x08 /* Private */,
      20,    0,  119,    2, 0x08 /* Private */,
      21,    0,  120,    2, 0x08 /* Private */,
      22,    0,  121,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 10,   11,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void TaskPage::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<TaskPage *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->questionAnswered((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->taskError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->imageUploaded((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->inferenceStarted(); break;
        case 4: _t->inferenceFinished((*reinterpret_cast< const InferenceResult(*)>(_a[1]))); break;
        case 5: _t->onTakePhoto(); break;
        case 6: _t->onUploadImage(); break;
        case 7: _t->onStartInference(); break;
        case 8: _t->onTestHostModel(); break;
        case 9: _t->onTestRK3588Model(); break;
        case 10: _t->onInferenceCompleted((*reinterpret_cast< const InferenceResult(*)>(_a[1]))); break;
        case 11: _t->onInferenceError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 12: _t->onNextQuestion(); break;
        case 13: _t->onPreviousQuestion(); break;
        case 14: _t->onAnswerSelected(); break;
        case 15: _t->updateInferenceProgress(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (TaskPage::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TaskPage::questionAnswered)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (TaskPage::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TaskPage::taskError)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (TaskPage::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TaskPage::imageUploaded)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (TaskPage::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TaskPage::inferenceStarted)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (TaskPage::*)(const InferenceResult & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TaskPage::inferenceFinished)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject TaskPage::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_TaskPage.data,
    qt_meta_data_TaskPage,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *TaskPage::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TaskPage::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TaskPage.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int TaskPage::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void TaskPage::questionAnswered(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void TaskPage::taskError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void TaskPage::imageUploaded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void TaskPage::inferenceStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void TaskPage::inferenceFinished(const InferenceResult & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
