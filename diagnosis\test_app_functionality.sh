#!/bin/bash

echo "=== 诊断应用程序功能测试 ==="

# 设置环境变量
export QT_QPA_PLATFORM=offscreen
export LD_LIBRARY_PATH=/opt/Qt5.12.6/5.12.6/gcc_64/lib:$LD_LIBRARY_PATH

cd /mnt/e/code/diagnosis/build

echo "1. 检查可执行文件..."
if [ -f "DiagnosisApp" ]; then
    echo "✓ 找到可执行文件: DiagnosisApp"
    ls -la DiagnosisApp
else
    echo "✗ 未找到可执行文件: DiagnosisApp"
    exit 1
fi

echo ""
echo "2. 检查依赖库..."
ldd DiagnosisApp | head -10

echo ""
echo "3. 测试应用程序启动（offscreen模式）..."
timeout 10s ./DiagnosisApp --help 2>&1 | head -20

echo ""
echo "4. 检查题目文件..."
if [ -f "config/questions.json" ]; then
    echo "✓ 题目文件存在"
    echo "题目数量: $(cat config/questions.json | grep -o '"Q"' | wc -l)"
else
    echo "✗ 题目文件不存在"
fi

echo ""
echo "5. 检查配置文件..."
if [ -f "config/default_tasks.json" ]; then
    echo "✓ 默认任务配置存在"
else
    echo "✗ 默认任务配置不存在"
fi

echo ""
echo "6. 检查目录结构..."
echo "目录结构:"
find . -type d | head -10

echo ""
echo "7. 检查文件权限..."
echo "可执行文件权限:"
ls -la DiagnosisApp

echo ""
echo "=== 测试完成 ==="

# 创建一个简单的启动脚本
echo ""
echo "8. 创建启动脚本..."
cat > start_diagnosis.sh << 'EOF'
#!/bin/bash
echo "启动药盒识别诊断应用程序..."
cd /mnt/e/code/diagnosis/build
export LD_LIBRARY_PATH=/opt/Qt5.12.6/5.12.6/gcc_64/lib:$LD_LIBRARY_PATH

# 检查X11显示
if [ -z "$DISPLAY" ]; then
    echo "警告: 未设置DISPLAY环境变量"
    echo "请确保X11转发已启用或在图形环境中运行"
    echo "尝试设置DISPLAY=:0"
    export DISPLAY=:0
fi

echo "当前DISPLAY: $DISPLAY"
echo "启动应用程序..."
./DiagnosisApp
EOF

chmod +x start_diagnosis.sh
echo "✓ 创建了启动脚本: start_diagnosis.sh"

echo ""
echo "使用方法:"
echo "1. 在WSL2中运行: ./start_diagnosis.sh"
echo "2. 确保Windows上的X11服务器正在运行"
echo "3. 或者在Linux图形环境中直接运行"
